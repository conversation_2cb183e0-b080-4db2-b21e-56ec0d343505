import { FeatureCollection, Point, MultiPoint, LineString, MultiLineString, Polygon, MultiPolygon, GeoJsonProperties } from 'geojson';

/**
 * Combines a {@link FeatureCollection} of {@link Point}, {@link LineString}, or {@link Polygon} features
 * into {@link MultiPoint}, {@link MultiLineString}, or {@link MultiPolygon} features.
 *
 * @function
 * @param {FeatureCollection<Point|LineString|Polygon>} fc a FeatureCollection of any type
 * @returns {FeatureCollection<MultiPoint|MultiLineString|MultiPolygon>} a FeatureCollection of corresponding type to input
 * @example
 * var fc = turf.featureCollection([
 *   turf.point([19.026432, 47.49134]),
 *   turf.point([19.074497, 47.509548])
 * ]);
 *
 * var combined = turf.combine(fc);
 *
 * //addToMap
 * var addToMap = [combined]
 */
declare function combine(fc: FeatureCollection<Point | MultiPoint | LineString | MultiLineString | Polygon | MultiPolygon>): FeatureCollection<MultiPoint | MultiLineString | MultiPolygon, {
    collectedProperties: GeoJsonProperties[];
}>;

export { combine, combine as default };
