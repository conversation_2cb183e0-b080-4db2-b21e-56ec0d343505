{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-bbox-polygon/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,wCAA4B;AAmB5B,SAAS,WAAA,CACP,IAAA,EACA,QAAA,EAGI,CAAC,CAAA,EACgB;AAIrB,EAAA,MAAM,KAAA,EAAO,MAAA,CAAO,IAAA,CAAK,CAAC,CAAC,CAAA;AAC3B,EAAA,MAAM,MAAA,EAAQ,MAAA,CAAO,IAAA,CAAK,CAAC,CAAC,CAAA;AAC5B,EAAA,MAAM,KAAA,EAAO,MAAA,CAAO,IAAA,CAAK,CAAC,CAAC,CAAA;AAC3B,EAAA,MAAM,MAAA,EAAQ,MAAA,CAAO,IAAA,CAAK,CAAC,CAAC,CAAA;AAE5B,EAAA,GAAA,CAAI,IAAA,CAAK,OAAA,IAAW,CAAA,EAAG;AACrB,IAAA,MAAM,IAAI,KAAA;AAAA,MACR;AAAA,IACF,CAAA;AAAA,EACF;AAEA,EAAA,MAAM,QAAA,EAAU,CAAC,IAAA,EAAM,KAAK,CAAA;AAC5B,EAAA,MAAM,QAAA,EAAU,CAAC,IAAA,EAAM,KAAK,CAAA;AAC5B,EAAA,MAAM,SAAA,EAAW,CAAC,IAAA,EAAM,KAAK,CAAA;AAC7B,EAAA,MAAM,SAAA,EAAW,CAAC,IAAA,EAAM,KAAK,CAAA;AAE7B,EAAA,OAAO,8BAAA;AAAA,IACL,CAAC,CAAC,OAAA,EAAS,QAAA,EAAU,QAAA,EAAU,OAAA,EAAS,OAAO,CAAC,CAAA;AAAA,IAChD,OAAA,CAAQ,UAAA;AAAA,IACR,EAAE,IAAA,EAAM,EAAA,EAAI,OAAA,CAAQ,GAAG;AAAA,EACzB,CAAA;AACF;AAGA,IAAO,0BAAA,EAAQ,WAAA;AD/Bf;AACE;AACA;AACF,+EAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-bbox-polygon/dist/cjs/index.cjs", "sourcesContent": [null, "import { BBox, Feature, Polygon, GeoJsonProperties } from \"geojson\";\nimport { polygon, Id } from \"@turf/helpers\";\n\n/**\n * Takes a bbox and returns an equivalent {@link Polygon|polygon}.\n *\n * @function\n * @param {BBox} bbox extent in [minX, minY, maxX, maxY] order\n * @param {Object} [options={}] Optional parameters\n * @param {GeoJsonProperties} [options.properties={}] Translate properties to Polygon\n * @param {string|number} [options.id={}] Translate Id to Polygon\n * @returns {Feature<Polygon>} a Polygon representation of the bounding box\n * @example\n * var bbox = [0, 0, 10, 10];\n *\n * var poly = turf.bboxPolygon(bbox);\n *\n * //addToMap\n * var addToMap = [poly]\n */\nfunction bboxPolygon<P extends GeoJsonProperties = GeoJsonProperties>(\n  bbox: BBox,\n  options: {\n    properties?: P;\n    id?: Id;\n  } = {}\n): Feature<Polygon, P> {\n  // Convert BBox positions to Numbers\n  // No performance loss for including Number()\n  // https://github.com/Turfjs/turf/issues/1119\n  const west = Number(bbox[0]);\n  const south = Number(bbox[1]);\n  const east = Number(bbox[2]);\n  const north = Number(bbox[3]);\n\n  if (bbox.length === 6) {\n    throw new Error(\n      \"@turf/bbox-polygon does not support BBox with 6 positions\"\n    );\n  }\n\n  const lowLeft = [west, south];\n  const topLeft = [west, north];\n  const topRight = [east, north];\n  const lowRight = [east, south];\n\n  return polygon(\n    [[lowLeft, lowRight, topRight, topLeft, lowLeft]],\n    options.properties,\n    { bbox, id: options.id }\n  );\n}\n\nexport { bboxPolygon };\nexport default bboxPolygon;\n"]}