# @turf/standard-deviational-ellipse

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## standardDeviationalEllipse

Takes a collection of features and returns a standard deviational ellipse,
also known as a “directional distribution.” The standard deviational ellipse
aims to show the direction and the distribution of a dataset by drawing
an ellipse that contains about one standard deviation’s worth (\~ 70%) of the
data.

This module mirrors the functionality of [Directional Distribution][1]
in ArcGIS and the [QGIS Standard Deviational Ellipse Plugin][2]

**Bibliography**

• <PERSON>, “The Standard Deviational Ellipse; An Updated Tool for
Spatial Description,” *Geografiska Annaler* 53, no. 1 (1971): 28–39,
doi:{@link [https://doi.org/10.2307/490885|10.2307/490885}][3].

• <PERSON>, “A Note on Lefever’s “Standard Deviational Ellipse,”
*American Journal of Sociology* 33, no. 1 (1927): 94—98,
doi:{@link [https://doi.org/10.1086/214336|10.1086/214336}][4].

### Parameters

*   `points` **[FeatureCollection][5]<[Point][6]>** GeoJSON points
*   `options` **[Object][7]** Optional parameters (optional, default `{}`)

    *   `options.weight` **[string][8]?** the property name used to weight the center
    *   `options.steps` **[number][9]** number of steps for the polygon (optional, default `64`)
    *   `options.properties` **[Object][7]** properties to pass to the resulting ellipse (optional, default `{}`)

### Examples

```javascript
const bbox = [-74, 40.72, -73.98, 40.74];
const points = turf.randomPoint(400, {bbox: bbox});
const sdEllipse = turf.standardDeviationalEllipse(points);

//addToMap
const addToMap = [points, sdEllipse];
```

Returns **[Feature][10]<[Polygon][11]>** an elliptical Polygon that includes approximately 1 SD of the dataset within it.

[1]: http://desktop.arcgis.com/en/arcmap/10.3/tools/spatial-statistics-toolbox/directional-distribution.htm

[2]: http://arken.nmbu.no/~havatv/gis/qgisplugins/SDEllipse/

[3]: https://doi.org/10.2307/490885|10.2307/490885}

[4]: https://doi.org/10.1086/214336|10.1086/214336}

[5]: https://tools.ietf.org/html/rfc7946#section-3.3

[6]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[8]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[9]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[10]: https://tools.ietf.org/html/rfc7946#section-3.2

[11]: https://tools.ietf.org/html/rfc7946#section-3.1.6

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/standard-deviational-ellipse
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
