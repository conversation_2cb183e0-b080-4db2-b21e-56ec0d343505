{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-bearing/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACAA,wCAA0D;AAC1D,4CAAyB;AA2BzB,SAAS,OAAA,CACP,KAAA,EACA,GAAA,EACA,QAAA,EAEI,CAAC,CAAA,EACG;AAER,EAAA,GAAA,CAAI,OAAA,CAAQ,MAAA,IAAU,IAAA,EAAM;AAC1B,IAAA,OAAO,qBAAA,CAAsB,KAAA,EAAO,GAAG,CAAA;AAAA,EACzC;AAEA,EAAA,MAAM,aAAA,EAAe,iCAAA,KAAc,CAAA;AACnC,EAAA,MAAM,aAAA,EAAe,iCAAA,GAAY,CAAA;AAEjC,EAAA,MAAM,KAAA,EAAO,uCAAA,YAAiB,CAAa,CAAC,CAAC,CAAA;AAC7C,EAAA,MAAM,KAAA,EAAO,uCAAA,YAAiB,CAAa,CAAC,CAAC,CAAA;AAC7C,EAAA,MAAM,KAAA,EAAO,uCAAA,YAAiB,CAAa,CAAC,CAAC,CAAA;AAC7C,EAAA,MAAM,KAAA,EAAO,uCAAA,YAAiB,CAAa,CAAC,CAAC,CAAA;AAC7C,EAAA,MAAM,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,KAAA,EAAO,IAAI,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,IAAI,CAAA;AAC/C,EAAA,MAAM,EAAA,EACJ,IAAA,CAAK,GAAA,CAAI,IAAI,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,IAAI,EAAA,EAC9B,IAAA,CAAK,GAAA,CAAI,IAAI,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,IAAI,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,KAAA,EAAO,IAAI,CAAA;AAExD,EAAA,OAAO,uCAAA,IAAiB,CAAK,KAAA,CAAM,CAAA,EAAG,CAAC,CAAC,CAAA;AAC1C;AAUA,SAAS,qBAAA,CAAsB,KAAA,EAAc,GAAA,EAAY;AAEvD,EAAA,IAAI,KAAA,EAAO,OAAA,CAAQ,GAAA,EAAK,KAAK,CAAA;AAC7B,EAAA,KAAA,EAAA,CAAQ,KAAA,EAAO,GAAA,EAAA,EAAO,GAAA;AACtB,EAAA,OAAO,IAAA;AACT;AAGA,IAAO,qBAAA,EAAQ,OAAA;ADhDf;AACE;AACA;AACF,kEAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-bearing/dist/cjs/index.cjs", "sourcesContent": [null, "import { Coord, degreesToRadians, radiansToDegrees } from \"@turf/helpers\";\nimport { getCoord } from \"@turf/invariant\";\n\n// http://en.wikipedia.org/wiki/Haversine_formula\n// http://www.movable-type.co.uk/scripts/latlong.html#bearing\n\n/**\n * Takes two {@link Point|points} and finds the geographic bearing between them,\n * i.e. the angle measured in degrees from the north line (0 degrees)\n *\n * @function\n * @param {Coord} start starting Point\n * @param {Coord} end ending Point\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.final=false] calculates the final bearing if true\n * @returns {number} bearing in decimal degrees, between -180 and 180 degrees (positive clockwise)\n * @example\n * var point1 = turf.point([-75.343, 39.984]);\n * var point2 = turf.point([-75.534, 39.123]);\n *\n * var bearing = turf.bearing(point1, point2);\n *\n * //addToMap\n * var addToMap = [point1, point2]\n * point1.properties['marker-color'] = '#f00'\n * point2.properties['marker-color'] = '#0f0'\n * point1.properties.bearing = bearing\n */\nfunction bearing(\n  start: Coord,\n  end: Coord,\n  options: {\n    final?: boolean;\n  } = {}\n): number {\n  // Reverse calculation\n  if (options.final === true) {\n    return calculateFinalBearing(start, end);\n  }\n\n  const coordinates1 = getCoord(start);\n  const coordinates2 = getCoord(end);\n\n  const lon1 = degreesToRadians(coordinates1[0]);\n  const lon2 = degreesToRadians(coordinates2[0]);\n  const lat1 = degreesToRadians(coordinates1[1]);\n  const lat2 = degreesToRadians(coordinates2[1]);\n  const a = Math.sin(lon2 - lon1) * Math.cos(lat2);\n  const b =\n    Math.cos(lat1) * Math.sin(lat2) -\n    Math.sin(lat1) * Math.cos(lat2) * Math.cos(lon2 - lon1);\n\n  return radiansToDegrees(Math.atan2(a, b));\n}\n\n/**\n * Calculates Final Bearing\n *\n * @private\n * @param {Coord} start starting Point\n * @param {Coord} end ending Point\n * @returns {number} bearing\n */\nfunction calculateFinalBearing(start: Coord, end: Coord) {\n  // Swap start & end\n  let bear = bearing(end, start);\n  bear = (bear + 180) % 360;\n  return bear;\n}\n\nexport { bearing };\nexport default bearing;\n"]}