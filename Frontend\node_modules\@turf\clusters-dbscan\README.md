# @turf/clusters-dbscan

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## Dbscan

Point classification within the cluster.

Type: (`"core"` | `"edge"` | `"noise"`)

## DbscanProps

**Extends GeoJsonProperties**

Properties assigned to each clustered point.

Type: [object][1]

### Properties

*   `dbscan` **[Dbscan][2]?** type of point it has been classified as
*   `cluster` **[number][3]?** associated clusterId

## clustersDbscan

Takes a set of [points][4] and partition them into clusters according to [DBSCAN's][5] data clustering algorithm.

### Parameters

*   `points` **[FeatureCollection][6]<[Point][4]>** to be clustered
*   `maxDistance` **[number][3]** Maximum Distance between any point of the cluster to generate the clusters (kilometers by default, see options)
*   `options` **[Object][1]** Optional parameters (optional, default `{}`)

    *   `options.units` **[string][7]** in which `maxDistance` is expressed, can be degrees, radians, miles, or kilometers (optional, default `"kilometers"`)
    *   `options.mutate` **[boolean][8]** Allows GeoJSON input to be mutated (optional, default `false`)
    *   `options.minPoints` **[number][3]** Minimum number of points to generate a single cluster,
        points which do not meet this requirement will be classified as an 'edge' or 'noise'. (optional, default `3`)

### Examples

```javascript
// create random points with random z-values in their properties
var points = turf.randomPoint(100, {bbox: [0, 30, 20, 50]});
var maxDistance = 100;
var clustered = turf.clustersDbscan(points, maxDistance);

//addToMap
var addToMap = [clustered];
```

Returns **[FeatureCollection][6]<[Point][4], [DbscanProps][9]>** Clustered Points with an additional two properties associated to each Feature:*   {number} cluster - the associated clusterId
*   {string} dbscan - type of point it has been classified as ('core'|'edge'|'noise')

[1]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[2]: #dbscan

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[4]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[5]: https://en.wikipedia.org/wiki/DBSCAN

[6]: https://tools.ietf.org/html/rfc7946#section-3.3

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[8]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

[9]: #dbscanprops

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/clusters-dbscan
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
