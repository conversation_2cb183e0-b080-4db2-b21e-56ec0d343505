{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-clusters-dbscan/dist/cjs/index.cjs", "../../index.ts", "../../lib/rbush-export.ts"], "names": [], "mappings": "AAAA;ACCA,oCAAsB;AACtB,0CAAyB;AACzB,wCAAyD;ADCzD;AACA;AEDA,4EAAgB;AAET,IAAM,MAAA,EAAQ,eAAA;AFErB;AACA;ACiDA,SAAS,cAAA,CACP,MAAA,EACA,WAAA,EACA,QAAA,EAII,CAAC,CAAA,EACkC;AAQvC,EAAA,GAAA,CAAI,OAAA,CAAQ,OAAA,IAAW,IAAA,EAAM,OAAA,EAAS,0BAAA,MAAY,CAAA;AAGlD,EAAA,MAAM,UAAA,EAAY,OAAA,CAAQ,UAAA,GAAa,CAAA;AAGvC,EAAA,MAAM,qBAAA,EAAuB,sCAAA,WAAgB,EAAa,OAAA,CAAQ,KAAK,CAAA;AAGvE,EAAA,IAAI,KAAA,EAAO,IAAI,KAAA,CAAM,MAAA,CAAO,QAAA,CAAS,MAAM,CAAA;AAG3C,EAAA,IAAI,QAAA,EAAU,MAAA,CAAO,QAAA,CAAS,GAAA,CAAI,CAAC,CAAA,EAAA,GAAM,KAAK,CAAA;AAG9C,EAAA,IAAI,SAAA,EAAW,MAAA,CAAO,QAAA,CAAS,GAAA,CAAI,CAAC,CAAA,EAAA,GAAM,KAAK,CAAA;AAG/C,EAAA,IAAI,QAAA,EAAU,MAAA,CAAO,QAAA,CAAS,GAAA,CAAI,CAAC,CAAA,EAAA,GAAM,KAAK,CAAA;AAG9C,EAAA,IAAI,WAAA,EAAuB,MAAA,CAAO,QAAA,CAAS,GAAA,CAAI,CAAC,CAAA,EAAA,GAAM,CAAA,CAAE,CAAA;AAGxD,EAAA,IAAA,CAAK,IAAA;AAAA,IACH,MAAA,CAAO,QAAA,CAAS,GAAA,CAAI,CAAC,KAAA,EAAO,KAAA,EAAA,GAAU;AACpC,MAAA,IAAI,CAAC,CAAA,EAAG,CAAC,EAAA,EAAI,KAAA,CAAM,QAAA,CAAS,WAAA;AAC5B,MAAA,OAAO;AAAA,QACL,IAAA,EAAM,CAAA;AAAA,QACN,IAAA,EAAM,CAAA;AAAA,QACN,IAAA,EAAM,CAAA;AAAA,QACN,IAAA,EAAM,CAAA;AAAA,QACN;AAAA,MACF,CAAA;AAAA,IACF,CAAC;AAAA,EACH,CAAA;AAGA,EAAA,MAAM,YAAA,EAAc,CAAC,KAAA,EAAA,GAAkC;AACrD,IAAA,MAAM,MAAA,EAAQ,MAAA,CAAO,QAAA,CAAS,KAAK,CAAA;AACnC,IAAA,MAAM,CAAC,CAAA,EAAG,CAAC,EAAA,EAAI,KAAA,CAAM,QAAA,CAAS,WAAA;AAE9B,IAAA,MAAM,KAAA,EAAO,IAAA,CAAK,GAAA,CAAI,EAAA,EAAI,oBAAA,EAAsB,CAAA,EAAK,CAAA;AACrD,IAAA,MAAM,KAAA,EAAO,IAAA,CAAK,GAAA,CAAI,EAAA,EAAI,oBAAA,EAAsB,EAAI,CAAA;AAEpD,IAAA,MAAM,qBAAA,EAAwB,QAAA,CAAA,EAAY;AAExC,MAAA,GAAA,CAAI,KAAA,EAAO,EAAA,GAAK,KAAA,EAAO,CAAA,EAAG;AACxB,QAAA,OAAO,oBAAA;AAAA,MACT;AACA,MAAA,GAAA,CAAI,IAAA,CAAK,GAAA,CAAI,IAAI,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,IAAI,CAAA,EAAG;AACnC,QAAA,OAAO,qBAAA,EAAuB,IAAA,CAAK,GAAA,CAAI,uCAAA,IAAqB,CAAC,CAAA;AAAA,MAC/D,EAAA,KAAO;AACL,QAAA,OAAO,qBAAA,EAAuB,IAAA,CAAK,GAAA,CAAI,uCAAA,IAAqB,CAAC,CAAA;AAAA,MAC/D;AAAA,IACF,CAAA,CAAG,CAAA;AAEH,IAAA,MAAM,KAAA,EAAO,IAAA,CAAK,GAAA,CAAI,EAAA,EAAI,oBAAA,EAAsB,CAAA,GAAM,CAAA;AACtD,IAAA,MAAM,KAAA,EAAO,IAAA,CAAK,GAAA,CAAI,EAAA,EAAI,oBAAA,EAAsB,GAAK,CAAA;AAGrD,IAAA,MAAM,KAAA,EAAO,EAAE,IAAA,EAAM,IAAA,EAAM,IAAA,EAAM,KAAK,CAAA;AACtC,IAAA,OAAQ,IAAA,CAAK,MAAA,CAAO,IAAI,CAAA,CAAkC,MAAA;AAAA,MACxD,CAAC,QAAA,EAAA,GAAa;AACZ,QAAA,MAAM,cAAA,EAAgB,QAAA,CAAS,KAAA;AAC/B,QAAA,MAAM,cAAA,EAAgB,MAAA,CAAO,QAAA,CAAS,aAAa,CAAA;AACnD,QAAA,MAAM,aAAA,EAAe,gCAAA,KAAS,EAAO,aAAA,EAAe;AAAA,UAClD,KAAA,EAAO;AAAA,QACT,CAAC,CAAA;AACD,QAAA,OAAO,aAAA,GAAgB,WAAA;AAAA,MACzB;AAAA,IACF,CAAA;AAAA,EACF,CAAA;AAGA,EAAA,MAAM,cAAA,EAAgB,CAAC,WAAA,EAAqB,SAAA,EAAA,GAA8B;AACxE,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,SAAA,CAAU,MAAA,EAAQ,CAAA,EAAA,EAAK;AACzC,MAAA,IAAI,SAAA,EAAW,SAAA,CAAU,CAAC,CAAA;AAC1B,MAAA,MAAM,cAAA,EAAgB,QAAA,CAAS,KAAA;AAC/B,MAAA,GAAA,CAAI,CAAC,OAAA,CAAQ,aAAa,CAAA,EAAG;AAC3B,QAAA,OAAA,CAAQ,aAAa,EAAA,EAAI,IAAA;AACzB,QAAA,MAAM,cAAA,EAAgB,WAAA,CAAY,aAAa,CAAA;AAC/C,QAAA,GAAA,CAAI,aAAA,CAAc,OAAA,GAAU,SAAA,EAAW;AACrC,UAAA,SAAA,CAAU,IAAA,CAAK,GAAG,aAAa,CAAA;AAAA,QACjC;AAAA,MACF;AACA,MAAA,GAAA,CAAI,CAAC,QAAA,CAAS,aAAa,CAAA,EAAG;AAC5B,QAAA,QAAA,CAAS,aAAa,EAAA,EAAI,IAAA;AAC1B,QAAA,UAAA,CAAW,aAAa,EAAA,EAAI,WAAA;AAAA,MAC9B;AAAA,IACF;AAAA,EACF,CAAA;AAGA,EAAA,IAAI,gBAAA,EAAkB,CAAA;AACtB,EAAA,MAAA,CAAO,QAAA,CAAS,OAAA,CAAQ,CAAC,CAAA,EAAG,KAAA,EAAA,GAAU;AACpC,IAAA,GAAA,CAAI,OAAA,CAAQ,KAAK,CAAA,EAAG,MAAA;AACpB,IAAA,MAAM,UAAA,EAAY,WAAA,CAAY,KAAK,CAAA;AACnC,IAAA,GAAA,CAAI,SAAA,CAAU,OAAA,GAAU,SAAA,EAAW;AACjC,MAAA,MAAM,YAAA,EAAc,eAAA;AACpB,MAAA,eAAA,EAAA;AACA,MAAA,OAAA,CAAQ,KAAK,EAAA,EAAI,IAAA;AACjB,MAAA,aAAA,CAAc,WAAA,EAAa,SAAS,CAAA;AAAA,IACtC,EAAA,KAAO;AACL,MAAA,OAAA,CAAQ,KAAK,EAAA,EAAI,IAAA;AAAA,IACnB;AAAA,EACF,CAAC,CAAA;AAGD,EAAA,MAAA,CAAO,QAAA,CAAS,OAAA,CAAQ,CAAC,CAAA,EAAG,KAAA,EAAA,GAAU;AACpC,IAAA,IAAI,aAAA,EAAe,MAAA,CAAO,QAAA,CAAS,KAAK,CAAA;AACxC,IAAA,GAAA,CAAI,CAAC,YAAA,CAAa,UAAA,EAAY;AAC5B,MAAA,YAAA,CAAa,WAAA,EAAa,CAAC,CAAA;AAAA,IAC7B;AAEA,IAAA,GAAA,CAAI,UAAA,CAAW,KAAK,EAAA,GAAK,CAAA,EAAG;AAC1B,MAAA,YAAA,CAAa,UAAA,CAAW,OAAA,EAAS,OAAA,CAAQ,KAAK,EAAA,EAAI,OAAA,EAAS,MAAA;AAC3D,MAAA,YAAA,CAAa,UAAA,CAAW,QAAA,EAAU,UAAA,CAAW,KAAK,CAAA;AAAA,IACpD,EAAA,KAAO;AACL,MAAA,YAAA,CAAa,UAAA,CAAW,OAAA,EAAS,OAAA;AAAA,IACnC;AAAA,EACF,CAAC,CAAA;AAED,EAAA,OAAO,MAAA;AACT;AAGA,IAAO,6BAAA,EAAQ,cAAA;ADhGf;AACE;AACA;AACF,wFAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-clusters-dbscan/dist/cjs/index.cjs", "sourcesContent": [null, "import { GeoJsonProperties, FeatureCollection, Point } from \"geojson\";\nimport { clone } from \"@turf/clone\";\nimport { distance } from \"@turf/distance\";\nimport { degreesToRadians, lengthToDegrees, Units } from \"@turf/helpers\";\nimport { rbush as RBush } from \"./lib/rbush-export.js\";\n\n/**\n * Point classification within the cluster.\n *\n * @typedef {\"core\" | \"edge\" | \"noise\"} Dbscan\n */\ntype Dbscan = \"core\" | \"edge\" | \"noise\";\n\n/**\n * Properties assigned to each clustered point.\n *\n * @extends GeoJsonProperties\n * @typedef {object} DbscanProps\n * @property {Dbscan} [dbscan] type of point it has been classified as\n * @property {number} [cluster] associated clusterId\n */\ntype DbscanProps = GeoJsonProperties & {\n  dbscan?: Dbscan;\n  cluster?: number;\n};\n\n// Structure of a point in the spatial index\ntype IndexedPoint = {\n  minX: number;\n  minY: number;\n  maxX: number;\n  maxY: number;\n  index: number;\n};\n\n/**\n * Takes a set of {@link Point|points} and partition them into clusters according to {@link https://en.wikipedia.org/wiki/DBSCAN|DBSCAN's} data clustering algorithm.\n *\n * @function\n * @param {FeatureCollection<Point>} points to be clustered\n * @param {number} maxDistance Maximum Distance between any point of the cluster to generate the clusters (kilometers by default, see options)\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.units=\"kilometers\"] in which `maxDistance` is expressed, can be degrees, radians, miles, or kilometers\n * @param {boolean} [options.mutate=false] Allows GeoJSON input to be mutated\n * @param {number} [options.minPoints=3] Minimum number of points to generate a single cluster,\n * points which do not meet this requirement will be classified as an 'edge' or 'noise'.\n * @returns {FeatureCollection<Point, DbscanProps>} Clustered Points with an additional two properties associated to each Feature:\n * - {number} cluster - the associated clusterId\n * - {string} dbscan - type of point it has been classified as ('core'|'edge'|'noise')\n * @example\n * // create random points with random z-values in their properties\n * var points = turf.randomPoint(100, {bbox: [0, 30, 20, 50]});\n * var maxDistance = 100;\n * var clustered = turf.clustersDbscan(points, maxDistance);\n *\n * //addToMap\n * var addToMap = [clustered];\n */\nfunction clustersDbscan(\n  points: FeatureCollection<Point>,\n  maxDistance: number,\n  options: {\n    units?: Units;\n    minPoints?: number;\n    mutate?: boolean;\n  } = {}\n): FeatureCollection<Point, DbscanProps> {\n  // Input validation being handled by Typescript\n  // collectionOf(points, 'Point', 'points must consist of a FeatureCollection of only Points');\n  // if (maxDistance === null || maxDistance === undefined) throw new Error('maxDistance is required');\n  // if (!(Math.sign(maxDistance) > 0)) throw new Error('maxDistance is invalid');\n  // if (!(minPoints === undefined || minPoints === null || Math.sign(minPoints) > 0)) throw new Error('options.minPoints is invalid');\n\n  // Clone points to prevent any mutations\n  if (options.mutate !== true) points = clone(points);\n\n  // Defaults\n  const minPoints = options.minPoints || 3;\n\n  // Calculate the distance in degrees for region queries\n  const latDistanceInDegrees = lengthToDegrees(maxDistance, options.units);\n\n  // Create a spatial index\n  var tree = new RBush(points.features.length);\n\n  // Keeps track of whether a point has been visited or not.\n  var visited = points.features.map((_) => false);\n\n  // Keeps track of whether a point is assigned to a cluster or not.\n  var assigned = points.features.map((_) => false);\n\n  // Keeps track of whether a point is noise|edge or not.\n  var isnoise = points.features.map((_) => false);\n\n  // Keeps track of the clusterId for each point\n  var clusterIds: number[] = points.features.map((_) => -1);\n\n  // Index each point for spatial queries\n  tree.load(\n    points.features.map((point, index) => {\n      var [x, y] = point.geometry.coordinates;\n      return {\n        minX: x,\n        minY: y,\n        maxX: x,\n        maxY: y,\n        index: index,\n      } as IndexedPoint;\n    })\n  );\n\n  // Function to find neighbors of a point within a given distance\n  const regionQuery = (index: number): IndexedPoint[] => {\n    const point = points.features[index];\n    const [x, y] = point.geometry.coordinates;\n\n    const minY = Math.max(y - latDistanceInDegrees, -90.0);\n    const maxY = Math.min(y + latDistanceInDegrees, 90.0);\n\n    const lonDistanceInDegrees = (function () {\n      // Handle the case where the bounding box crosses the poles\n      if (minY < 0 && maxY > 0) {\n        return latDistanceInDegrees;\n      }\n      if (Math.abs(minY) < Math.abs(maxY)) {\n        return latDistanceInDegrees / Math.cos(degreesToRadians(maxY));\n      } else {\n        return latDistanceInDegrees / Math.cos(degreesToRadians(minY));\n      }\n    })();\n\n    const minX = Math.max(x - lonDistanceInDegrees, -360.0);\n    const maxX = Math.min(x + lonDistanceInDegrees, 360.0);\n\n    // Calculate the bounding box for the region query\n    const bbox = { minX, minY, maxX, maxY };\n    return (tree.search(bbox) as ReadonlyArray<IndexedPoint>).filter(\n      (neighbor) => {\n        const neighborIndex = neighbor.index;\n        const neighborPoint = points.features[neighborIndex];\n        const distanceInKm = distance(point, neighborPoint, {\n          units: \"kilometers\",\n        });\n        return distanceInKm <= maxDistance;\n      }\n    );\n  };\n\n  // Function to expand a cluster\n  const expandCluster = (clusteredId: number, neighbors: IndexedPoint[]) => {\n    for (var i = 0; i < neighbors.length; i++) {\n      var neighbor = neighbors[i];\n      const neighborIndex = neighbor.index;\n      if (!visited[neighborIndex]) {\n        visited[neighborIndex] = true;\n        const nextNeighbors = regionQuery(neighborIndex);\n        if (nextNeighbors.length >= minPoints) {\n          neighbors.push(...nextNeighbors);\n        }\n      }\n      if (!assigned[neighborIndex]) {\n        assigned[neighborIndex] = true;\n        clusterIds[neighborIndex] = clusteredId;\n      }\n    }\n  };\n\n  // Main DBSCAN clustering algorithm\n  var nextClusteredId = 0;\n  points.features.forEach((_, index) => {\n    if (visited[index]) return;\n    const neighbors = regionQuery(index);\n    if (neighbors.length >= minPoints) {\n      const clusteredId = nextClusteredId;\n      nextClusteredId++;\n      visited[index] = true;\n      expandCluster(clusteredId, neighbors);\n    } else {\n      isnoise[index] = true;\n    }\n  });\n\n  // Assign DBSCAN properties to each point\n  points.features.forEach((_, index) => {\n    var clusterPoint = points.features[index];\n    if (!clusterPoint.properties) {\n      clusterPoint.properties = {};\n    }\n\n    if (clusterIds[index] >= 0) {\n      clusterPoint.properties.dbscan = isnoise[index] ? \"edge\" : \"core\";\n      clusterPoint.properties.cluster = clusterIds[index];\n    } else {\n      clusterPoint.properties.dbscan = \"noise\";\n    }\n  });\n\n  return points as FeatureCollection<Point, DbscanProps>;\n}\n\nexport { Dbscan, DbscanProps, clustersDbscan };\nexport default clustersDbscan;\n", "// Get around problems with moduleResolution node16 and some older libraries.\n// Manifests as \"This expression is not callable ... has no call signatures\"\n// https://stackoverflow.com/a/74709714\n\nimport lib from \"rbush\";\n\nexport const rbush = lib as unknown as typeof lib.default;\n"]}