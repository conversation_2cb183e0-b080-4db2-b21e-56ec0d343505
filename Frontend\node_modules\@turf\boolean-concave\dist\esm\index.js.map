{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Feature, Polygon } from \"geojson\";\nimport { getGeom } from \"@turf/invariant\";\n\n/**\n * Takes a polygon and return true or false as to whether it is concave or not.\n *\n * @function\n * @param {Feature<Polygon>} polygon to be evaluated\n * @returns {boolean} true/false\n * @example\n * var convexPolygon = turf.polygon([[[0,0],[0,1],[1,1],[1,0],[0,0]]]);\n *\n * turf.booleanConcave(convexPolygon)\n * //=false\n */\nfunction booleanConcave(polygon: Feature<Polygon> | Polygon) {\n  // Taken from https://stackoverflow.com/a/1881201 & https://stackoverflow.com/a/25304159\n  const coords = getGeom(polygon).coordinates;\n  if (coords[0].length <= 4) {\n    return false;\n  }\n\n  let sign = false;\n  const n = coords[0].length - 1;\n  for (let i = 0; i < n; i++) {\n    const dx1 = coords[0][(i + 2) % n][0] - coords[0][(i + 1) % n][0];\n    const dy1 = coords[0][(i + 2) % n][1] - coords[0][(i + 1) % n][1];\n    const dx2 = coords[0][i][0] - coords[0][(i + 1) % n][0];\n    const dy2 = coords[0][i][1] - coords[0][(i + 1) % n][1];\n    const zcrossproduct = dx1 * dy2 - dy1 * dx2;\n    if (i === 0) {\n      sign = zcrossproduct > 0;\n    } else if (sign !== zcrossproduct > 0) {\n      return true;\n    }\n  }\n  return false;\n}\n\nexport { booleanConcave };\nexport default booleanConcave;\n"], "mappings": ";AACA,SAAS,eAAe;AAcxB,SAAS,eAAe,SAAqC;AAE3D,QAAM,SAAS,QAAQ,OAAO,EAAE;AAChC,MAAI,OAAO,CAAC,EAAE,UAAU,GAAG;AACzB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO;AACX,QAAM,IAAI,OAAO,CAAC,EAAE,SAAS;AAC7B,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAM,MAAM,OAAO,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC;AAChE,UAAM,MAAM,OAAO,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC;AAChE,UAAM,MAAM,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC;AACtD,UAAM,MAAM,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,OAAO,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC;AACtD,UAAM,gBAAgB,MAAM,MAAM,MAAM;AACxC,QAAI,MAAM,GAAG;AACX,aAAO,gBAAgB;AAAA,IACzB,WAAW,SAAS,gBAAgB,GAAG;AACrC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAGA,IAAO,+BAAQ;", "names": []}