{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { GeoJsonProperties, Feature, Point, Polygon } from \"geojson\";\nimport { destination } from \"@turf/destination\";\nimport { polygon, Units } from \"@turf/helpers\";\n\n/**\n * Takes a {@link Point} and calculates the circle polygon given a radius in degrees, radians, miles, or kilometers; and steps for precision.\n *\n * @function\n * @param {Feature<Point>|number[]} center center point\n * @param {number} radius radius of the circle\n * @param {Object} [options={}] Optional parameters\n * @param {number} [options.steps=64] number of steps\n * @param {string} [options.units='kilometers'] miles, kilometers, degrees, or radians\n * @param {Object} [options.properties={}] properties\n * @returns {Feature<Polygon>} circle polygon\n * @example\n * var center = [-75.343, 39.984];\n * var radius = 5;\n * var options = {steps: 10, units: 'kilometers', properties: {foo: 'bar'}};\n * var circle = turf.circle(center, radius, options);\n *\n * //addToMap\n * var addToMap = [turf.point(center), circle]\n */\nfunction circle<P extends GeoJsonProperties = GeoJsonProperties>(\n  center: number[] | Point | Feature<Point, P>,\n  radius: number,\n  options: {\n    steps?: number;\n    units?: Units;\n    properties?: P;\n  } = {}\n): Feature<Polygon, P> {\n  // default params\n  const steps = options.steps || 64;\n  const properties: any = options.properties\n    ? options.properties\n    : !Array.isArray(center) && center.type === \"Feature\" && center.properties\n      ? center.properties\n      : {};\n\n  // main\n  const coordinates = [];\n  for (let i = 0; i < steps; i++) {\n    coordinates.push(\n      destination(center, radius, (i * -360) / steps, options).geometry\n        .coordinates\n    );\n  }\n  coordinates.push(coordinates[0]);\n\n  return polygon([coordinates], properties);\n}\n\nexport { circle };\nexport default circle;\n"], "mappings": ";AACA,SAAS,mBAAmB;AAC5B,SAAS,eAAsB;AAsB/B,SAAS,OACP,QACA,QACA,UAII,CAAC,GACgB;AAErB,QAAM,QAAQ,QAAQ,SAAS;AAC/B,QAAM,aAAkB,QAAQ,aAC5B,QAAQ,aACR,CAAC,MAAM,QAAQ,MAAM,KAAK,OAAO,SAAS,aAAa,OAAO,aAC5D,OAAO,aACP,CAAC;AAGP,QAAM,cAAc,CAAC;AACrB,WAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,gBAAY;AAAA,MACV,YAAY,QAAQ,QAAS,IAAI,OAAQ,OAAO,OAAO,EAAE,SACtD;AAAA,IACL;AAAA,EACF;AACA,cAAY,KAAK,YAAY,CAAC,CAAC;AAE/B,SAAO,QAAQ,CAAC,WAAW,GAAG,UAAU;AAC1C;AAGA,IAAO,sBAAQ;", "names": []}