// index.ts
import { clone } from "@turf/clone";
import { coordAll, featureEach } from "@turf/meta";
import skmeans from "skmeans";
function clustersKmeans(points, options = {}) {
  var count = points.features.length;
  options.numberOfClusters = options.numberOfClusters || Math.round(Math.sqrt(count / 2));
  if (options.numberOfClusters > count) options.numberOfClusters = count;
  if (options.mutate !== true) points = clone(points);
  var data = coordAll(points);
  var initialCentroids = data.slice(0, options.numberOfClusters);
  var skmeansResult = skmeans(data, options.numberOfClusters, initialCentroids);
  var centroids = {};
  skmeansResult.centroids.forEach(function(coord, idx) {
    centroids[idx] = coord;
  });
  featureEach(points, function(point, index) {
    var clusterId = skmeansResult.idxs[index];
    point.properties.cluster = clusterId;
    point.properties.centroid = centroids[clusterId];
  });
  return points;
}
var turf_clusters_kmeans_default = clustersKmeans;
export {
  clustersKmeans,
  turf_clusters_kmeans_default as default
};
//# sourceMappingURL=index.js.map