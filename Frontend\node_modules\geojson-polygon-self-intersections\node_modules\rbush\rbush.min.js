!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).rbush=t()}}(function(){return function t(i,n,e){function r(h,o){if(!n[h]){if(!i[h]){var s="function"==typeof require&&require;if(!o&&s)return s(h,!0);if(a)return a(h,!0);var f=new Error("Cannot find module '"+h+"'");throw f.code="MODULE_NOT_FOUND",f}var l=n[h]={exports:{}};i[h][0].call(l.exports,function(t){var n=i[h][1][t];return r(n||t)},l,l.exports,t,i,n,e)}return n[h].exports}for(var a="function"==typeof require&&require,h=0;h<e.length;h++)r(e[h]);return r}({1:[function(t,i,n){"use strict";function e(t,i){if(!(this instanceof e))return new e(t,i);this._maxEntries=Math.max(4,t||9),this._minEntries=Math.max(2,Math.ceil(.4*this._maxEntries)),i&&this._initFormat(i),this.clear()}function r(t,i){a(t,0,t.children.length,i,t)}function a(t,i,n,e,r){r||(r=m(null)),r.minX=1/0,r.minY=1/0,r.maxX=-1/0,r.maxY=-1/0;for(var a,o=i;o<n;o++)a=t.children[o],h(r,t.leaf?e(a):a);return r}function h(t,i){return t.minX=Math.min(t.minX,i.minX),t.minY=Math.min(t.minY,i.minY),t.maxX=Math.max(t.maxX,i.maxX),t.maxY=Math.max(t.maxY,i.maxY),t}function o(t,i){return t.minX-i.minX}function s(t,i){return t.minY-i.minY}function f(t){return(t.maxX-t.minX)*(t.maxY-t.minY)}function l(t){return t.maxX-t.minX+(t.maxY-t.minY)}function u(t,i){return t.minX<=i.minX&&t.minY<=i.minY&&i.maxX<=t.maxX&&i.maxY<=t.maxY}function c(t,i){return i.minX<=t.maxX&&i.minY<=t.maxY&&i.maxX>=t.minX&&i.maxY>=t.minY}function m(t){return{children:t,height:1,leaf:!0,minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0}}function d(t,i,n,e,r){for(var a,h=[i,n];h.length;)(n=h.pop())-(i=h.pop())<=e||(a=i+Math.ceil((n-i)/e/2)*e,x(t,a,i,n,r),h.push(i,a,a,n))}i.exports=e,i.exports.default=e;var x=t("quickselect");e.prototype={all:function(){return this._all(this.data,[])},search:function(t){var i=this.data,n=[],e=this.toBBox;if(!c(t,i))return n;for(var r,a,h,o,s=[];i;){for(r=0,a=i.children.length;r<a;r++)h=i.children[r],c(t,o=i.leaf?e(h):h)&&(i.leaf?n.push(h):u(t,o)?this._all(h,n):s.push(h));i=s.pop()}return n},collides:function(t){var i=this.data,n=this.toBBox;if(!c(t,i))return!1;for(var e,r,a,h,o=[];i;){for(e=0,r=i.children.length;e<r;e++)if(a=i.children[e],h=i.leaf?n(a):a,c(t,h)){if(i.leaf||u(t,h))return!0;o.push(a)}i=o.pop()}return!1},load:function(t){if(!t||!t.length)return this;if(t.length<this._minEntries){for(var i=0,n=t.length;i<n;i++)this.insert(t[i]);return this}var e=this._build(t.slice(),0,t.length-1,0);if(this.data.children.length)if(this.data.height===e.height)this._splitRoot(this.data,e);else{if(this.data.height<e.height){var r=this.data;this.data=e,e=r}this._insert(e,this.data.height-e.height-1,!0)}else this.data=e;return this},insert:function(t){return t&&this._insert(t,this.data.height-1),this},clear:function(){return this.data=m([]),this},remove:function(t,i){if(!t)return this;for(var n,e,r,a,h=this.data,o=this.toBBox(t),s=[],f=[];h||s.length;){if(h||(h=s.pop(),e=s[s.length-1],n=f.pop(),a=!0),h.leaf&&-1!==(r=function(t,i,n){if(!n)return i.indexOf(t);for(var e=0;e<i.length;e++)if(n(t,i[e]))return e;return-1}(t,h.children,i)))return h.children.splice(r,1),s.push(h),this._condense(s),this;a||h.leaf||!u(h,o)?e?(n++,h=e.children[n],a=!1):h=null:(s.push(h),f.push(n),n=0,e=h,h=h.children[0])}return this},toBBox:function(t){return t},compareMinX:o,compareMinY:s,toJSON:function(){return this.data},fromJSON:function(t){return this.data=t,this},_all:function(t,i){for(var n=[];t;)t.leaf?i.push.apply(i,t.children):n.push.apply(n,t.children),t=n.pop();return i},_build:function(t,i,n,e){var a,h=n-i+1,o=this._maxEntries;if(h<=o)return a=m(t.slice(i,n+1)),r(a,this.toBBox),a;e||(e=Math.ceil(Math.log(h)/Math.log(o)),o=Math.ceil(h/Math.pow(o,e-1))),(a=m([])).leaf=!1,a.height=e;var s,f,l,u,c=Math.ceil(h/o),x=c*Math.ceil(Math.sqrt(o));for(d(t,i,n,x,this.compareMinX),s=i;s<=n;s+=x)for(d(t,s,l=Math.min(s+x-1,n),c,this.compareMinY),f=s;f<=l;f+=c)u=Math.min(f+c-1,l),a.children.push(this._build(t,f,u,e-1));return r(a,this.toBBox),a},_chooseSubtree:function(t,i,n,e){for(var r,a,h,o,s,l,u,c;e.push(i),!i.leaf&&e.length-1!==n;){for(u=c=1/0,r=0,a=i.children.length;r<a;r++)s=f(h=i.children[r]),(l=function(t,i){return(Math.max(i.maxX,t.maxX)-Math.min(i.minX,t.minX))*(Math.max(i.maxY,t.maxY)-Math.min(i.minY,t.minY))}(t,h)-s)<c?(c=l,u=s<u?s:u,o=h):l===c&&s<u&&(u=s,o=h);i=o||i.children[0]}return i},_insert:function(t,i,n){var e=this.toBBox,r=n?t:e(t),a=[],o=this._chooseSubtree(r,this.data,i,a);for(o.children.push(t),h(o,r);i>=0&&a[i].children.length>this._maxEntries;)this._split(a,i),i--;this._adjustParentBBoxes(r,a,i)},_split:function(t,i){var n=t[i],e=n.children.length,a=this._minEntries;this._chooseSplitAxis(n,a,e);var h=this._chooseSplitIndex(n,a,e),o=m(n.children.splice(h,n.children.length-h));o.height=n.height,o.leaf=n.leaf,r(n,this.toBBox),r(o,this.toBBox),i?t[i-1].children.push(o):this._splitRoot(n,o)},_splitRoot:function(t,i){this.data=m([t,i]),this.data.height=t.height+1,this.data.leaf=!1,r(this.data,this.toBBox)},_chooseSplitIndex:function(t,i,n){var e,r,h,o,s,l,u,c;for(l=u=1/0,e=i;e<=n-i;e++)o=function(t,i){var n=Math.max(t.minX,i.minX),e=Math.max(t.minY,i.minY),r=Math.min(t.maxX,i.maxX),a=Math.min(t.maxY,i.maxY);return Math.max(0,r-n)*Math.max(0,a-e)}(r=a(t,0,e,this.toBBox),h=a(t,e,n,this.toBBox)),s=f(r)+f(h),o<l?(l=o,c=e,u=s<u?s:u):o===l&&s<u&&(u=s,c=e);return c},_chooseSplitAxis:function(t,i,n){var e=t.leaf?this.compareMinX:o,r=t.leaf?this.compareMinY:s;this._allDistMargin(t,i,n,e)<this._allDistMargin(t,i,n,r)&&t.children.sort(e)},_allDistMargin:function(t,i,n,e){t.children.sort(e);var r,o,s=this.toBBox,f=a(t,0,i,s),u=a(t,n-i,n,s),c=l(f)+l(u);for(r=i;r<n-i;r++)o=t.children[r],h(f,t.leaf?s(o):o),c+=l(f);for(r=n-i-1;r>=i;r--)o=t.children[r],h(u,t.leaf?s(o):o),c+=l(u);return c},_adjustParentBBoxes:function(t,i,n){for(var e=n;e>=0;e--)h(i[e],t)},_condense:function(t){for(var i,n=t.length-1;n>=0;n--)0===t[n].children.length?n>0?(i=t[n-1].children).splice(i.indexOf(t[n]),1):this.clear():r(t[n],this.toBBox)},_initFormat:function(t){var i=["return a"," - b",";"];this.compareMinX=new Function("a","b",i.join(t[0])),this.compareMinY=new Function("a","b",i.join(t[1])),this.toBBox=new Function("a","return {minX: a"+t[0]+", minY: a"+t[1]+", maxX: a"+t[2]+", maxY: a"+t[3]+"};")}}},{quickselect:2}],2:[function(t,i,n){"use strict";function e(t,i,n,e,a){r(t,i,n||0,e||t.length-1,a||function(t,i){return t<i?-1:t>i?1:0})}function r(t,i,n,e,h){for(;e>n;){if(e-n>600){var o=e-n+1,s=i-n+1,f=Math.log(o),l=.5*Math.exp(2*f/3),u=.5*Math.sqrt(f*l*(o-l)/o)*(s-o/2<0?-1:1);r(t,i,Math.max(n,Math.floor(i-s*l/o+u)),Math.min(e,Math.floor(i+(o-s)*l/o+u)),h)}var c=t[i],m=n,d=e;for(a(t,n,i),h(t[e],c)>0&&a(t,n,e);m<d;){for(a(t,m,d),m++,d--;h(t[m],c)<0;)m++;for(;h(t[d],c)>0;)d--}0===h(t[n],c)?a(t,n,d):a(t,++d,e),d<=i&&(n=d+1),i<=d&&(e=d-1)}}function a(t,i,n){var e=t[i];t[i]=t[n],t[n]=e}i.exports=e,i.exports.default=e},{}]},{},[1])(1)});
