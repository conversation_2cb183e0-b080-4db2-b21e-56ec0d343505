{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-circle/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,gDAA4B;AAC5B,wCAA+B;AAsB/B,SAAS,MAAA,CACP,MAAA,EACA,MAAA,EACA,QAAA,EAII,CAAC,CAAA,EACgB;AAErB,EAAA,MAAM,MAAA,EAAQ,OAAA,CAAQ,MAAA,GAAS,EAAA;AAC/B,EAAA,MAAM,WAAA,EAAkB,OAAA,CAAQ,WAAA,EAC5B,OAAA,CAAQ,WAAA,EACR,CAAC,KAAA,CAAM,OAAA,CAAQ,MAAM,EAAA,GAAK,MAAA,CAAO,KAAA,IAAS,UAAA,GAAa,MAAA,CAAO,WAAA,EAC5D,MAAA,CAAO,WAAA,EACP,CAAC,CAAA;AAGP,EAAA,MAAM,YAAA,EAAc,CAAC,CAAA;AACrB,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,KAAA,EAAO,CAAA,EAAA,EAAK;AAC9B,IAAA,WAAA,CAAY,IAAA;AAAA,MACV,sCAAA,MAAY,EAAQ,MAAA,EAAS,EAAA,EAAI,CAAA,IAAA,EAAQ,KAAA,EAAO,OAAO,CAAA,CAAE,QAAA,CACtD;AAAA,IACL,CAAA;AAAA,EACF;AACA,EAAA,WAAA,CAAY,IAAA,CAAK,WAAA,CAAY,CAAC,CAAC,CAAA;AAE/B,EAAA,OAAO,8BAAA,CAAS,WAAW,CAAA,EAAG,UAAU,CAAA;AAC1C;AAGA,IAAO,oBAAA,EAAQ,MAAA;ADvCf;AACE;AACA;AACF,+DAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-circle/dist/cjs/index.cjs", "sourcesContent": [null, "import { GeoJsonProperties, Feature, Point, Polygon } from \"geojson\";\nimport { destination } from \"@turf/destination\";\nimport { polygon, Units } from \"@turf/helpers\";\n\n/**\n * Takes a {@link Point} and calculates the circle polygon given a radius in degrees, radians, miles, or kilometers; and steps for precision.\n *\n * @function\n * @param {Feature<Point>|number[]} center center point\n * @param {number} radius radius of the circle\n * @param {Object} [options={}] Optional parameters\n * @param {number} [options.steps=64] number of steps\n * @param {string} [options.units='kilometers'] miles, kilometers, degrees, or radians\n * @param {Object} [options.properties={}] properties\n * @returns {Feature<Polygon>} circle polygon\n * @example\n * var center = [-75.343, 39.984];\n * var radius = 5;\n * var options = {steps: 10, units: 'kilometers', properties: {foo: 'bar'}};\n * var circle = turf.circle(center, radius, options);\n *\n * //addToMap\n * var addToMap = [turf.point(center), circle]\n */\nfunction circle<P extends GeoJsonProperties = GeoJsonProperties>(\n  center: number[] | Point | Feature<Point, P>,\n  radius: number,\n  options: {\n    steps?: number;\n    units?: Units;\n    properties?: P;\n  } = {}\n): Feature<Polygon, P> {\n  // default params\n  const steps = options.steps || 64;\n  const properties: any = options.properties\n    ? options.properties\n    : !Array.isArray(center) && center.type === \"Feature\" && center.properties\n      ? center.properties\n      : {};\n\n  // main\n  const coordinates = [];\n  for (let i = 0; i < steps; i++) {\n    coordinates.push(\n      destination(center, radius, (i * -360) / steps, options).geometry\n        .coordinates\n    );\n  }\n  coordinates.push(coordinates[0]);\n\n  return polygon([coordinates], properties);\n}\n\nexport { circle };\nexport default circle;\n"]}