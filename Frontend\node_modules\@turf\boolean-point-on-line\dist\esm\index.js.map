{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Feature, LineString } from \"geojson\";\nimport { Coord } from \"@turf/helpers\";\nimport { getCoord, getCoords } from \"@turf/invariant\";\n\n/**\n * Returns true if a point is on a line. Accepts a optional parameter to ignore the\n * start and end vertices of the linestring.\n *\n * @function\n * @param {Coord} pt GeoJSON Point\n * @param {Feature<LineString>} line GeoJSON LineString\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.ignoreEndVertices=false] whether to ignore the start and end vertices.\n * @param {number} [options.epsilon] Fractional number to compare with the cross product result. Useful for dealing with floating points such as lng/lat points\n * @returns {boolean} true/false\n * @example\n * var pt = turf.point([0, 0]);\n * var line = turf.lineString([[-1, -1],[1, 1],[1.5, 2.2]]);\n * var isPointOnLine = turf.booleanPointOnLine(pt, line);\n * //=true\n */\nfunction booleanPointOnLine(\n  pt: Coord,\n  line: Feature<LineString> | LineString,\n  options: {\n    ignoreEndVertices?: boolean;\n    epsilon?: number;\n  } = {}\n): boolean {\n  // Normalize inputs\n  const ptCoords = getCoord(pt);\n  const lineCoords = getCoords(line);\n\n  // Main\n  for (let i = 0; i < lineCoords.length - 1; i++) {\n    let ignoreBoundary: boolean | string = false;\n    if (options.ignoreEndVertices) {\n      if (i === 0) {\n        ignoreBoundary = \"start\";\n      }\n      if (i === lineCoords.length - 2) {\n        ignoreBoundary = \"end\";\n      }\n      if (i === 0 && i + 1 === lineCoords.length - 1) {\n        ignoreBoundary = \"both\";\n      }\n    }\n    if (\n      isPointOnLineSegment(\n        lineCoords[i],\n        lineCoords[i + 1],\n        ptCoords,\n        ignoreBoundary,\n        typeof options.epsilon === \"undefined\" ? null : options.epsilon\n      )\n    ) {\n      return true;\n    }\n  }\n  return false;\n}\n\n// See http://stackoverflow.com/a/4833823/1979085\n// See https://stackoverflow.com/a/328122/1048847\n/**\n * @private\n * @param {Position} lineSegmentStart coord pair of start of line\n * @param {Position} lineSegmentEnd coord pair of end of line\n * @param {Position} pt coord pair of point to check\n * @param {boolean|string} excludeBoundary whether the point is allowed to fall on the line ends.\n * @param {number} epsilon Fractional number to compare with the cross product result. Useful for dealing with floating points such as lng/lat points\n * If true which end to ignore.\n * @returns {boolean} true/false\n */\nfunction isPointOnLineSegment(\n  lineSegmentStart: number[],\n  lineSegmentEnd: number[],\n  pt: number[],\n  excludeBoundary: string | boolean,\n  epsilon: number | null\n): boolean {\n  const x = pt[0];\n  const y = pt[1];\n  const x1 = lineSegmentStart[0];\n  const y1 = lineSegmentStart[1];\n  const x2 = lineSegmentEnd[0];\n  const y2 = lineSegmentEnd[1];\n  const dxc = pt[0] - x1;\n  const dyc = pt[1] - y1;\n  const dxl = x2 - x1;\n  const dyl = y2 - y1;\n  const cross = dxc * dyl - dyc * dxl;\n  if (epsilon !== null) {\n    if (Math.abs(cross) > epsilon) {\n      return false;\n    }\n  } else if (cross !== 0) {\n    return false;\n  }\n\n  // Special cases for zero length lines\n  // https://github.com/Turfjs/turf/issues/2750\n  if (Math.abs(dxl) === Math.abs(dyl) && Math.abs(dxl) === 0) {\n    // Zero length line.\n    if (excludeBoundary) {\n      // To be on a zero length line pt has to be on the start (and end), BUT we\n      // are excluding start and end from possible matches.\n      return false;\n    }\n    if (pt[0] === lineSegmentStart[0] && pt[1] === lineSegmentStart[1]) {\n      // If point is same as start (and end) it's on the line segment\n      return true;\n    } else {\n      // Otherwise point is somewhere else\n      return false;\n    }\n  }\n\n  if (!excludeBoundary) {\n    if (Math.abs(dxl) >= Math.abs(dyl)) {\n      return dxl > 0 ? x1 <= x && x <= x2 : x2 <= x && x <= x1;\n    }\n    return dyl > 0 ? y1 <= y && y <= y2 : y2 <= y && y <= y1;\n  } else if (excludeBoundary === \"start\") {\n    if (Math.abs(dxl) >= Math.abs(dyl)) {\n      return dxl > 0 ? x1 < x && x <= x2 : x2 <= x && x < x1;\n    }\n    return dyl > 0 ? y1 < y && y <= y2 : y2 <= y && y < y1;\n  } else if (excludeBoundary === \"end\") {\n    if (Math.abs(dxl) >= Math.abs(dyl)) {\n      return dxl > 0 ? x1 <= x && x < x2 : x2 < x && x <= x1;\n    }\n    return dyl > 0 ? y1 <= y && y < y2 : y2 < y && y <= y1;\n  } else if (excludeBoundary === \"both\") {\n    if (Math.abs(dxl) >= Math.abs(dyl)) {\n      return dxl > 0 ? x1 < x && x < x2 : x2 < x && x < x1;\n    }\n    return dyl > 0 ? y1 < y && y < y2 : y2 < y && y < y1;\n  }\n  return false;\n}\n\nexport { booleanPointOnLine };\nexport default booleanPointOnLine;\n"], "mappings": ";AAEA,SAAS,UAAU,iBAAiB;AAmBpC,SAAS,mBACP,IACA,MACA,UAGI,CAAC,GACI;AAET,QAAM,WAAW,SAAS,EAAE;AAC5B,QAAM,aAAa,UAAU,IAAI;AAGjC,WAAS,IAAI,GAAG,IAAI,WAAW,SAAS,GAAG,KAAK;AAC9C,QAAI,iBAAmC;AACvC,QAAI,QAAQ,mBAAmB;AAC7B,UAAI,MAAM,GAAG;AACX,yBAAiB;AAAA,MACnB;AACA,UAAI,MAAM,WAAW,SAAS,GAAG;AAC/B,yBAAiB;AAAA,MACnB;AACA,UAAI,MAAM,KAAK,IAAI,MAAM,WAAW,SAAS,GAAG;AAC9C,yBAAiB;AAAA,MACnB;AAAA,IACF;AACA,QACE;AAAA,MACE,WAAW,CAAC;AAAA,MACZ,WAAW,IAAI,CAAC;AAAA,MAChB;AAAA,MACA;AAAA,MACA,OAAO,QAAQ,YAAY,cAAc,OAAO,QAAQ;AAAA,IAC1D,GACA;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAcA,SAAS,qBACP,kBACA,gBACA,IACA,iBACA,SACS;AACT,QAAM,IAAI,GAAG,CAAC;AACd,QAAM,IAAI,GAAG,CAAC;AACd,QAAM,KAAK,iBAAiB,CAAC;AAC7B,QAAM,KAAK,iBAAiB,CAAC;AAC7B,QAAM,KAAK,eAAe,CAAC;AAC3B,QAAM,KAAK,eAAe,CAAC;AAC3B,QAAM,MAAM,GAAG,CAAC,IAAI;AACpB,QAAM,MAAM,GAAG,CAAC,IAAI;AACpB,QAAM,MAAM,KAAK;AACjB,QAAM,MAAM,KAAK;AACjB,QAAM,QAAQ,MAAM,MAAM,MAAM;AAChC,MAAI,YAAY,MAAM;AACpB,QAAI,KAAK,IAAI,KAAK,IAAI,SAAS;AAC7B,aAAO;AAAA,IACT;AAAA,EACF,WAAW,UAAU,GAAG;AACtB,WAAO;AAAA,EACT;AAIA,MAAI,KAAK,IAAI,GAAG,MAAM,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,MAAM,GAAG;AAE1D,QAAI,iBAAiB;AAGnB,aAAO;AAAA,IACT;AACA,QAAI,GAAG,CAAC,MAAM,iBAAiB,CAAC,KAAK,GAAG,CAAC,MAAM,iBAAiB,CAAC,GAAG;AAElE,aAAO;AAAA,IACT,OAAO;AAEL,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,CAAC,iBAAiB;AACpB,QAAI,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,GAAG;AAClC,aAAO,MAAM,IAAI,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK;AAAA,IACxD;AACA,WAAO,MAAM,IAAI,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK;AAAA,EACxD,WAAW,oBAAoB,SAAS;AACtC,QAAI,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,GAAG;AAClC,aAAO,MAAM,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,IAAI;AAAA,IACtD;AACA,WAAO,MAAM,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,IAAI;AAAA,EACtD,WAAW,oBAAoB,OAAO;AACpC,QAAI,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,GAAG;AAClC,aAAO,MAAM,IAAI,MAAM,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK;AAAA,IACtD;AACA,WAAO,MAAM,IAAI,MAAM,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK;AAAA,EACtD,WAAW,oBAAoB,QAAQ;AACrC,QAAI,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,GAAG;AAClC,aAAO,MAAM,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI;AAAA,IACpD;AACA,WAAO,MAAM,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI;AAAA,EACpD;AACA,SAAO;AACT;AAGA,IAAO,qCAAQ;", "names": []}