# @turf/circle

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## circle

Takes a [Point][1] and calculates the circle polygon given a radius in degrees, radians, miles, or kilometers; and steps for precision.

### Parameters

*   `center` **([Feature][2]<[Point][1]> | [Array][3]<[number][4]>)** center point
*   `radius` **[number][4]** radius of the circle
*   `options` **[Object][5]** Optional parameters (optional, default `{}`)

    *   `options.steps` **[number][4]** number of steps (optional, default `64`)
    *   `options.units` **[string][6]** miles, kilometers, degrees, or radians (optional, default `'kilometers'`)
    *   `options.properties` **[Object][5]** properties (optional, default `{}`)

### Examples

```javascript
var center = [-75.343, 39.984];
var radius = 5;
var options = {steps: 10, units: 'kilometers', properties: {foo: 'bar'}};
var circle = turf.circle(center, radius, options);

//addToMap
var addToMap = [turf.point(center), circle]
```

Returns **[Feature][2]<[Polygon][7]>** circle polygon

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[2]: https://tools.ietf.org/html/rfc7946#section-3.2

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[7]: https://tools.ietf.org/html/rfc7946#section-3.1.6

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/circle
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
