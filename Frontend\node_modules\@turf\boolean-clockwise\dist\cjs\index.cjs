"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _invariant = require('@turf/invariant');
function booleanClockwise(line) {
  const ring = _invariant.getCoords.call(void 0, line);
  let sum = 0;
  let i = 1;
  let prev;
  let cur;
  while (i < ring.length) {
    prev = cur || ring[0];
    cur = ring[i];
    sum += (cur[0] - prev[0]) * (cur[1] + prev[1]);
    i++;
  }
  return sum > 0;
}
var turf_boolean_clockwise_default = booleanClockwise;



exports.booleanClockwise = booleanClockwise; exports.default = turf_boolean_clockwise_default;
//# sourceMappingURL=index.cjs.map