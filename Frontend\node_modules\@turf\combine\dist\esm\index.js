// index.ts
import { feature, featureCollection } from "@turf/helpers";
import { featureEach } from "@turf/meta";
function combine(fc) {
  var groups = {
    MultiPoint: {
      coordinates: [],
      properties: []
    },
    MultiLineString: {
      coordinates: [],
      properties: []
    },
    MultiPolygon: {
      coordinates: [],
      properties: []
    }
  };
  featureEach(fc, (feature2) => {
    var _a;
    switch ((_a = feature2.geometry) == null ? void 0 : _a.type) {
      case "Point":
        groups.MultiPoint.coordinates.push(feature2.geometry.coordinates);
        groups.MultiPoint.properties.push(feature2.properties);
        break;
      case "MultiPoint":
        groups.MultiPoint.coordinates.push(...feature2.geometry.coordinates);
        groups.MultiPoint.properties.push(feature2.properties);
        break;
      case "LineString":
        groups.MultiLineString.coordinates.push(feature2.geometry.coordinates);
        groups.MultiLineString.properties.push(feature2.properties);
        break;
      case "MultiLineString":
        groups.MultiLineString.coordinates.push(
          ...feature2.geometry.coordinates
        );
        groups.MultiLineString.properties.push(feature2.properties);
        break;
      case "Polygon":
        groups.MultiPolygon.coordinates.push(feature2.geometry.coordinates);
        groups.MultiPolygon.properties.push(feature2.properties);
        break;
      case "MultiPolygon":
        groups.MultiPolygon.coordinates.push(...feature2.geometry.coordinates);
        groups.MultiPolygon.properties.push(feature2.properties);
        break;
      default:
        break;
    }
  });
  return featureCollection(
    Object.keys(groups).filter(function(key) {
      return groups[key].coordinates.length;
    }).sort().map(function(key) {
      var geometry = { type: key, coordinates: groups[key].coordinates };
      var properties = { collectedProperties: groups[key].properties };
      return feature(geometry, properties);
    })
  );
}
var turf_combine_default = combine;
export {
  combine,
  turf_combine_default as default
};
//# sourceMappingURL=index.js.map