{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-hex-grid/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACAA,0CAAyB;AACzB,4CAA0B;AAQ1B,wCAAkD;AA2BlD,SAAS,OAAA,CACP,IAAA,EACA,QAAA,EACA,QAAA,EAKI,CAAC,CAAA,EAC0B;AAE/B,EAAA,MAAM,iBAAA,EAAmB,IAAA,CAAK,SAAA,CAAU,OAAA,CAAQ,WAAA,GAAc,CAAC,CAAC,CAAA;AAEhE,EAAA,MAAM,CAAC,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,KAAK,EAAA,EAAI,IAAA;AACnC,EAAA,MAAM,QAAA,EAAA,CAAW,MAAA,EAAQ,KAAA,EAAA,EAAS,CAAA;AAClC,EAAA,MAAM,QAAA,EAAA,CAAW,KAAA,EAAO,IAAA,EAAA,EAAQ,CAAA;AAGhC,EAAA,MAAM,UAAA,EACH,SAAA,EAAW,EAAA,EAAK,gCAAA,CAAU,IAAA,EAAM,OAAO,CAAA,EAAG,CAAC,IAAA,EAAM,OAAO,CAAA,EAAG,OAAO,CAAA;AACrE,EAAA,MAAM,UAAA,EAAY,UAAA,EAAA,CAAa,KAAA,EAAO,IAAA,CAAA;AACtC,EAAA,MAAM,UAAA,EACH,SAAA,EAAW,EAAA,EAAK,gCAAA,CAAU,OAAA,EAAS,KAAK,CAAA,EAAG,CAAC,OAAA,EAAS,KAAK,CAAA,EAAG,OAAO,CAAA;AACvE,EAAA,MAAM,WAAA,EAAa,UAAA,EAAA,CAAa,MAAA,EAAQ,KAAA,CAAA;AACxC,EAAA,MAAM,OAAA,EAAS,UAAA,EAAY,CAAA;AAE3B,EAAA,MAAM,UAAA,EAAY,OAAA,EAAS,CAAA;AAC3B,EAAA,MAAM,WAAA,EAAc,IAAA,CAAK,IAAA,CAAK,CAAC,EAAA,EAAI,EAAA,EAAK,UAAA;AAExC,EAAA,MAAM,UAAA,EAAY,KAAA,EAAO,IAAA;AACzB,EAAA,MAAM,WAAA,EAAa,MAAA,EAAQ,KAAA;AAE3B,EAAA,MAAM,WAAA,EAAc,EAAA,EAAI,EAAA,EAAK,SAAA;AAC7B,EAAA,MAAM,WAAA,EAAa,UAAA;AAGnB,EAAA,MAAM,OAAA,EAAA,CAAU,UAAA,EAAY,SAAA,EAAA,EAAA,CAAc,UAAA,EAAY,OAAA,EAAS,CAAA,CAAA;AAC/D,EAAA,MAAM,QAAA,EAAU,IAAA,CAAK,KAAA,CAAM,MAAM,CAAA;AAEjC,EAAA,MAAM,SAAA,EAAA,CACH,QAAA,EAAU,WAAA,EAAa,OAAA,EAAS,EAAA,EAAI,SAAA,EAAA,EAAa,EAAA,EAClD,OAAA,EAAS,EAAA,EACT,WAAA,EAAa,CAAA;AAGf,EAAA,MAAM,QAAA,EAAU,IAAA,CAAK,KAAA,CAAA,CAAO,WAAA,EAAa,UAAA,EAAA,EAAc,UAAU,CAAA;AAEjE,EAAA,IAAI,SAAA,EAAA,CAAY,WAAA,EAAa,QAAA,EAAU,UAAA,EAAA,EAAc,CAAA;AAErD,EAAA,MAAM,WAAA,EAAa,QAAA,EAAU,WAAA,EAAa,WAAA,EAAa,WAAA,EAAa,CAAA;AACpE,EAAA,GAAA,CAAI,UAAA,EAAY;AACd,IAAA,SAAA,GAAY,WAAA,EAAa,CAAA;AAAA,EAC3B;AAGA,EAAA,MAAM,QAAA,EAAU,CAAC,CAAA;AACjB,EAAA,MAAM,MAAA,EAAQ,CAAC,CAAA;AACf,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,CAAA,EAAG,CAAA,EAAA,EAAK;AAC1B,IAAA,MAAM,MAAA,EAAU,EAAA,EAAI,IAAA,CAAK,GAAA,EAAM,EAAA,EAAK,CAAA;AACpC,IAAA,OAAA,CAAQ,IAAA,CAAK,IAAA,CAAK,GAAA,CAAI,KAAK,CAAC,CAAA;AAC5B,IAAA,KAAA,CAAM,IAAA,CAAK,IAAA,CAAK,GAAA,CAAI,KAAK,CAAC,CAAA;AAAA,EAC5B;AAEA,EAAA,MAAM,QAAA,EAAU,CAAC,CAAA;AACjB,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,GAAK,OAAA,EAAS,CAAA,EAAA,EAAK;AACjC,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,GAAK,OAAA,EAAS,CAAA,EAAA,EAAK;AACjC,MAAA,MAAM,MAAA,EAAQ,EAAA,EAAI,EAAA,IAAM,CAAA;AACxB,MAAA,GAAA,CAAI,EAAA,IAAM,EAAA,GAAK,KAAA,EAAO,QAAA;AACtB,MAAA,GAAA,CAAI,EAAA,IAAM,EAAA,GAAK,UAAA,EAAY,QAAA;AAE3B,MAAA,MAAM,SAAA,EAAW,EAAA,EAAI,WAAA,EAAa,KAAA,EAAO,QAAA;AACzC,MAAA,IAAI,SAAA,EAAW,EAAA,EAAI,WAAA,EAAa,MAAA,EAAQ,QAAA;AAExC,MAAA,GAAA,CAAI,KAAA,EAAO;AACT,QAAA,SAAA,GAAY,WAAA,EAAa,CAAA;AAAA,MAC3B;AAEA,MAAA,GAAA,CAAI,OAAA,CAAQ,UAAA,IAAc,IAAA,EAAM;AAC9B,QAAA,YAAA;AAAA,UACE,CAAC,QAAA,EAAU,QAAQ,CAAA;AAAA,UACnB,UAAA,EAAY,CAAA;AAAA,UACZ,WAAA,EAAa,CAAA;AAAA,UACb,IAAA,CAAK,KAAA,CAAM,gBAAgB,CAAA;AAAA,UAC3B,OAAA;AAAA,UACA;AAAA,QACF,CAAA,CAAE,OAAA,CAAQ,QAAA,CAAU,QAAA,EAAU;AAC5B,UAAA,GAAA,CAAI,OAAA,CAAQ,IAAA,EAAM;AAChB,YAAA,GAAA,CAAI,kCAAA,wCAAU,CAAmB,OAAA,CAAQ,IAAA,EAAM,QAAQ,CAAC,CAAC,CAAA;AACvD,cAAA,OAAA,CAAQ,IAAA,CAAK,QAAQ,CAAA;AAAA,UACzB,EAAA,KAAO;AACL,YAAA,OAAA,CAAQ,IAAA,CAAK,QAAQ,CAAA;AAAA,UACvB;AAAA,QACF,CAAC,CAAA;AAAA,MACH,EAAA,KAAO;AACL,QAAA,MAAM,IAAA,EAAM,OAAA;AAAA,UACV,CAAC,QAAA,EAAU,QAAQ,CAAA;AAAA,UACnB,UAAA,EAAY,CAAA;AAAA,UACZ,WAAA,EAAa,CAAA;AAAA,UACb,IAAA,CAAK,KAAA,CAAM,gBAAgB,CAAA;AAAA,UAC3B,OAAA;AAAA,UACA;AAAA,QACF,CAAA;AACA,QAAA,GAAA,CAAI,OAAA,CAAQ,IAAA,EAAM;AAChB,UAAA,GAAA,CAAI,kCAAA,wCAAU,CAAmB,OAAA,CAAQ,IAAA,EAAM,GAAG,CAAC,CAAC,CAAA;AAClD,YAAA,OAAA,CAAQ,IAAA,CAAK,GAAG,CAAA;AAAA,QACpB,EAAA,KAAO;AACL,UAAA,OAAA,CAAQ,IAAA,CAAK,GAAG,CAAA;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,EAAA,OAAO,wCAAA,OAAyB,CAAA;AAClC;AAcA,SAAS,OAAA,CACP,MAAA,EACA,EAAA,EACA,EAAA,EACA,UAAA,EACA,OAAA,EACA,KAAA,EACA;AACA,EAAA,MAAM,SAAA,EAAW,CAAC,CAAA;AAClB,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,CAAA,EAAG,CAAA,EAAA,EAAK;AAC1B,IAAA,MAAM,EAAA,EAAI,MAAA,CAAO,CAAC,EAAA,EAAI,GAAA,EAAK,OAAA,CAAQ,CAAC,CAAA;AACpC,IAAA,MAAM,EAAA,EAAI,MAAA,CAAO,CAAC,EAAA,EAAI,GAAA,EAAK,KAAA,CAAM,CAAC,CAAA;AAClC,IAAA,QAAA,CAAS,IAAA,CAAK,CAAC,CAAA,EAAG,CAAC,CAAC,CAAA;AAAA,EACtB;AAEA,EAAA,QAAA,CAAS,IAAA,CAAK,QAAA,CAAS,CAAC,CAAA,CAAE,KAAA,CAAM,CAAC,CAAA;AACjC,EAAA,OAAO,8BAAA,CAAS,QAAQ,CAAA,EAAG,UAAU,CAAA;AACvC;AAcA,SAAS,YAAA,CACP,MAAA,EACA,EAAA,EACA,EAAA,EACA,UAAA,EACA,OAAA,EACA,KAAA,EACA;AACA,EAAA,MAAM,UAAA,EAAY,CAAC,CAAA;AACnB,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,CAAA,EAAG,CAAA,EAAA,EAAK;AAC1B,IAAA,MAAM,SAAA,EAAW,CAAC,CAAA;AAClB,IAAA,QAAA,CAAS,IAAA,CAAK,MAAM,CAAA;AACpB,IAAA,QAAA,CAAS,IAAA,CAAK,CAAC,MAAA,CAAO,CAAC,EAAA,EAAI,GAAA,EAAK,OAAA,CAAQ,CAAC,CAAA,EAAG,MAAA,CAAO,CAAC,EAAA,EAAI,GAAA,EAAK,KAAA,CAAM,CAAC,CAAC,CAAC,CAAA;AACtE,IAAA,QAAA,CAAS,IAAA,CAAK;AAAA,MACZ,MAAA,CAAO,CAAC,EAAA,EAAI,GAAA,EAAK,OAAA,CAAA,CAAS,EAAA,EAAI,CAAA,EAAA,EAAK,CAAC,CAAA;AAAA,MACpC,MAAA,CAAO,CAAC,EAAA,EAAI,GAAA,EAAK,KAAA,CAAA,CAAO,EAAA,EAAI,CAAA,EAAA,EAAK,CAAC;AAAA,IACpC,CAAC,CAAA;AACD,IAAA,QAAA,CAAS,IAAA,CAAK,MAAM,CAAA;AACpB,IAAA,SAAA,CAAU,IAAA,CAAK,8BAAA,CAAS,QAAQ,CAAA,EAAG,UAAU,CAAC,CAAA;AAAA,EAChD;AACA,EAAA,OAAO,SAAA;AACT;AAGA,IAAO,sBAAA,EAAQ,OAAA;AD7Gf;AACE;AACA;AACF,mEAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-hex-grid/dist/cjs/index.cjs", "sourcesContent": [null, "import { distance } from \"@turf/distance\";\nimport { intersect } from \"@turf/intersect\";\nimport {\n  Feature,\n  FeatureCollection,\n  GeoJsonProperties,\n  Polygon,\n  BBox,\n} from \"geojson\";\nimport { polygon, featureCollection, Units } from \"@turf/helpers\";\n\n/**\n * Takes a bounding box and the diameter of the cell and returns a {@link FeatureCollection} of flat-topped\n * hexagons or triangles ({@link Polygon} features) aligned in an \"odd-q\" vertical grid as\n * described in [Hexagonal Grids](http://www.redblobgames.com/grids/hexagons/).\n *\n * @function\n * @param {BBox} bbox extent in [minX, minY, maxX, maxY] order\n * @param {number} cellSide length of the side of the the hexagons or triangles, in units. It will also coincide with the\n * radius of the circumcircle of the hexagons.\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.units='kilometers'] used in calculating cell size, can be degrees, radians, miles, or kilometers\n * @param {Object} [options.properties={}] passed to each hexagon or triangle of the grid\n * @param {Feature<Polygon>} [options.mask] if passed a Polygon or MultiPolygon, the grid Points will be created only inside it\n * @param {boolean} [options.triangles=false] whether to return as triangles instead of hexagons\n * @returns {FeatureCollection<Polygon>} a hexagonal grid\n * @example\n * var bbox = [-96,31,-84,40];\n * var cellSide = 50;\n * var options = {units: 'miles'};\n *\n * var hexgrid = turf.hexGrid(bbox, cellSide, options);\n *\n * //addToMap\n * var addToMap = [hexgrid];\n */\nfunction hexGrid<P extends GeoJsonProperties = GeoJsonProperties>(\n  bbox: BBox,\n  cellSide: number,\n  options: {\n    units?: Units;\n    triangles?: boolean;\n    properties?: P;\n    mask?: Feature<Polygon>;\n  } = {}\n): FeatureCollection<Polygon, P> {\n  // Issue => https://github.com/Turfjs/turf/issues/1284\n  const clonedProperties = JSON.stringify(options.properties || {});\n\n  const [west, south, east, north] = bbox;\n  const centerY = (south + north) / 2;\n  const centerX = (west + east) / 2;\n\n  // https://github.com/Turfjs/turf/issues/758\n  const xFraction =\n    (cellSide * 2) / distance([west, centerY], [east, centerY], options);\n  const cellWidth = xFraction * (east - west);\n  const yFraction =\n    (cellSide * 2) / distance([centerX, south], [centerX, north], options);\n  const cellHeight = yFraction * (north - south);\n  const radius = cellWidth / 2;\n\n  const hex_width = radius * 2;\n  const hex_height = (Math.sqrt(3) / 2) * cellHeight;\n\n  const box_width = east - west;\n  const box_height = north - south;\n\n  const x_interval = (3 / 4) * hex_width;\n  const y_interval = hex_height;\n\n  // adjust box_width so all hexagons will be inside the bbox\n  const x_span = (box_width - hex_width) / (hex_width - radius / 2);\n  const x_count = Math.floor(x_span);\n\n  const x_adjust =\n    (x_count * x_interval - radius / 2 - box_width) / 2 -\n    radius / 2 +\n    x_interval / 2;\n\n  // adjust box_height so all hexagons will be inside the bbox\n  const y_count = Math.floor((box_height - hex_height) / hex_height);\n\n  let y_adjust = (box_height - y_count * hex_height) / 2;\n\n  const hasOffsetY = y_count * hex_height - box_height > hex_height / 2;\n  if (hasOffsetY) {\n    y_adjust -= hex_height / 4;\n  }\n\n  // Precompute cosines and sines of angles used in hexagon creation for performance gain\n  const cosines = [];\n  const sines = [];\n  for (let i = 0; i < 6; i++) {\n    const angle = ((2 * Math.PI) / 6) * i;\n    cosines.push(Math.cos(angle));\n    sines.push(Math.sin(angle));\n  }\n\n  const results = [];\n  for (let x = 0; x <= x_count; x++) {\n    for (let y = 0; y <= y_count; y++) {\n      const isOdd = x % 2 === 1;\n      if (y === 0 && isOdd) continue;\n      if (y === 0 && hasOffsetY) continue;\n\n      const center_x = x * x_interval + west - x_adjust;\n      let center_y = y * y_interval + south + y_adjust;\n\n      if (isOdd) {\n        center_y -= hex_height / 2;\n      }\n\n      if (options.triangles === true) {\n        hexTriangles(\n          [center_x, center_y],\n          cellWidth / 2,\n          cellHeight / 2,\n          JSON.parse(clonedProperties),\n          cosines,\n          sines\n        ).forEach(function (triangle) {\n          if (options.mask) {\n            if (intersect(featureCollection([options.mask, triangle])))\n              results.push(triangle);\n          } else {\n            results.push(triangle);\n          }\n        });\n      } else {\n        const hex = hexagon(\n          [center_x, center_y],\n          cellWidth / 2,\n          cellHeight / 2,\n          JSON.parse(clonedProperties),\n          cosines,\n          sines\n        );\n        if (options.mask) {\n          if (intersect(featureCollection([options.mask, hex])))\n            results.push(hex);\n        } else {\n          results.push(hex);\n        }\n      }\n    }\n  }\n\n  return featureCollection(results) as FeatureCollection<Polygon, P>;\n}\n\n/**\n * Creates hexagon\n *\n * @private\n * @param {Array<number>} center of the hexagon\n * @param {number} rx half hexagon width\n * @param {number} ry half hexagon height\n * @param {Object} properties passed to each hexagon\n * @param {Array<number>} cosines precomputed\n * @param {Array<number>} sines precomputed\n * @returns {Feature<Polygon>} hexagon\n */\nfunction hexagon(\n  center: number[],\n  rx: number,\n  ry: number,\n  properties: GeoJsonProperties,\n  cosines: number[],\n  sines: number[]\n) {\n  const vertices = [];\n  for (let i = 0; i < 6; i++) {\n    const x = center[0] + rx * cosines[i];\n    const y = center[1] + ry * sines[i];\n    vertices.push([x, y]);\n  }\n  //first and last vertex must be the same\n  vertices.push(vertices[0].slice());\n  return polygon([vertices], properties);\n}\n\n/**\n * Creates triangles composing an hexagon\n *\n * @private\n * @param {Array<number>} center of the hexagon\n * @param {number} rx half triangle width\n * @param {number} ry half triangle height\n * @param {Object} properties passed to each triangle\n * @param {Array<number>} cosines precomputed\n * @param {Array<number>} sines precomputed\n * @returns {Array<Feature<Polygon>>} triangles\n */\nfunction hexTriangles(\n  center: number[],\n  rx: number,\n  ry: number,\n  properties: GeoJsonProperties,\n  cosines: number[],\n  sines: number[]\n) {\n  const triangles = [];\n  for (let i = 0; i < 6; i++) {\n    const vertices = [];\n    vertices.push(center);\n    vertices.push([center[0] + rx * cosines[i], center[1] + ry * sines[i]]);\n    vertices.push([\n      center[0] + rx * cosines[(i + 1) % 6],\n      center[1] + ry * sines[(i + 1) % 6],\n    ]);\n    vertices.push(center);\n    triangles.push(polygon([vertices], properties));\n  }\n  return triangles;\n}\n\nexport { hexGrid };\nexport default hexGrid;\n"]}