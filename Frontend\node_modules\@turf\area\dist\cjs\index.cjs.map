{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-area/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,wCAA4B;AAC5B,kCAA2B;AAiB3B,SAAS,IAAA,CAAK,OAAA,EAA2D;AACvE,EAAA,OAAO,8BAAA;AAAA,IACL,OAAA;AAAA,IACA,CAAC,KAAA,EAAO,IAAA,EAAA,GAAS;AACf,MAAA,OAAO,MAAA,EAAQ,aAAA,CAAc,IAAI,CAAA;AAAA,IACnC,CAAA;AAAA,IACA;AAAA,EACF,CAAA;AACF;AASA,SAAS,aAAA,CAAc,IAAA,EAAwB;AAC7C,EAAA,IAAI,MAAA,EAAQ,CAAA;AACZ,EAAA,IAAI,CAAA;AACJ,EAAA,OAAA,CAAQ,IAAA,CAAK,IAAA,EAAM;AAAA,IACjB,KAAK,SAAA;AACH,MAAA,OAAO,WAAA,CAAY,IAAA,CAAK,WAAW,CAAA;AAAA,IACrC,KAAK,cAAA;AACH,MAAA,IAAA,CAAK,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,IAAA,CAAK,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AAC5C,QAAA,MAAA,GAAS,WAAA,CAAY,IAAA,CAAK,WAAA,CAAY,CAAC,CAAC,CAAA;AAAA,MAC1C;AACA,MAAA,OAAO,KAAA;AAAA,IACT,KAAK,OAAA;AAAA,IACL,KAAK,YAAA;AAAA,IACL,KAAK,YAAA;AAAA,IACL,KAAK,iBAAA;AACH,MAAA,OAAO,CAAA;AAAA,EACX;AACA,EAAA,OAAO,CAAA;AACT;AAEA,SAAS,WAAA,CAAY,MAAA,EAAa;AAChC,EAAA,IAAI,MAAA,EAAQ,CAAA;AACZ,EAAA,GAAA,CAAI,OAAA,GAAU,MAAA,CAAO,OAAA,EAAS,CAAA,EAAG;AAC/B,IAAA,MAAA,GAAS,IAAA,CAAK,GAAA,CAAI,QAAA,CAAS,MAAA,CAAO,CAAC,CAAC,CAAC,CAAA;AACrC,IAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,MAAA,CAAO,MAAA,EAAQ,CAAA,EAAA,EAAK;AACtC,MAAA,MAAA,GAAS,IAAA,CAAK,GAAA,CAAI,QAAA,CAAS,MAAA,CAAO,CAAC,CAAC,CAAC,CAAA;AAAA,IACvC;AAAA,EACF;AACA,EAAA,OAAO,KAAA;AACT;AASA,IAAM,OAAA,EAAU,qBAAA,EAAc,qBAAA,EAAe,CAAA;AAS7C,IAAM,YAAA,EAAc,IAAA,CAAK,GAAA,EAAK,GAAA;AAe9B,SAAS,QAAA,CAAS,MAAA,EAA4B;AAC5C,EAAA,MAAM,aAAA,EAAe,MAAA,CAAO,OAAA,EAAS,CAAA;AAErC,EAAA,GAAA,CAAI,aAAA,GAAgB,CAAA,EAAG,OAAO,CAAA;AAC9B,EAAA,IAAI,MAAA,EAAQ,CAAA;AAEZ,EAAA,IAAI,EAAA,EAAI,CAAA;AACR,EAAA,MAAA,CAAO,EAAA,EAAI,YAAA,EAAc;AACvB,IAAA,MAAM,MAAA,EAAQ,MAAA,CAAO,CAAC,CAAA;AACtB,IAAA,MAAM,OAAA,EAAS,MAAA,CAAO,EAAA,EAAI,EAAA,IAAM,aAAA,EAAe,EAAA,EAAI,EAAA,EAAI,CAAC,CAAA;AACxD,IAAA,MAAM,MAAA,EACJ,MAAA,CAAO,EAAA,EAAI,EAAA,GAAK,aAAA,EAAA,CAAgB,EAAA,EAAI,CAAA,EAAA,EAAK,aAAA,EAAe,EAAA,EAAI,CAAC,CAAA;AAE/D,IAAA,MAAM,OAAA,EAAS,KAAA,CAAM,CAAC,EAAA,EAAI,WAAA;AAC1B,IAAA,MAAM,QAAA,EAAU,MAAA,CAAO,CAAC,EAAA,EAAI,WAAA;AAC5B,IAAA,MAAM,OAAA,EAAS,KAAA,CAAM,CAAC,EAAA,EAAI,WAAA;AAE1B,IAAA,MAAA,GAAA,CAAU,OAAA,EAAS,MAAA,EAAA,EAAU,IAAA,CAAK,GAAA,CAAI,OAAO,CAAA;AAE7C,IAAA,CAAA,EAAA;AAAA,EACF;AAEA,EAAA,OAAO,MAAA,EAAQ,MAAA;AACjB;AAGA,IAAO,kBAAA,EAAQ,IAAA;AD/Df;AACE;AACA;AACF,yDAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-area/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, FeatureCollection, Geometry } from \"geojson\";\nimport { earthRadius } from \"@turf/helpers\";\nimport { geomReduce } from \"@turf/meta\";\n\n/**\n * Calculates the geodesic area in square meters of one or more polygons.\n *\n * @function\n * @param {GeoJSON} geojson input polygon(s) as {@link Geometry}, {@link Feature}, or {@link FeatureCollection}\n * @returns {number} area in square meters\n * @example\n * var polygon = turf.polygon([[[125, -15], [113, -22], [154, -27], [144, -15], [125, -15]]]);\n *\n * var area = turf.area(polygon);\n *\n * //addToMap\n * var addToMap = [polygon]\n * polygon.properties.area = area\n */\nfunction area(geojson: Feature<any> | FeatureCollection<any> | Geometry) {\n  return geomReduce(\n    geojson,\n    (value, geom) => {\n      return value + calculateArea(geom);\n    },\n    0\n  );\n}\n\n/**\n * Calculate Area\n *\n * @private\n * @param {Geometry} geom GeoJSON Geometries\n * @returns {number} area\n */\nfunction calculateArea(geom: Geometry): number {\n  let total = 0;\n  let i;\n  switch (geom.type) {\n    case \"Polygon\":\n      return polygonArea(geom.coordinates);\n    case \"MultiPolygon\":\n      for (i = 0; i < geom.coordinates.length; i++) {\n        total += polygonArea(geom.coordinates[i]);\n      }\n      return total;\n    case \"Point\":\n    case \"MultiPoint\":\n    case \"LineString\":\n    case \"MultiLineString\":\n      return 0;\n  }\n  return 0;\n}\n\nfunction polygonArea(coords: any) {\n  let total = 0;\n  if (coords && coords.length > 0) {\n    total += Math.abs(ringArea(coords[0]));\n    for (let i = 1; i < coords.length; i++) {\n      total -= Math.abs(ringArea(coords[i]));\n    }\n  }\n  return total;\n}\n\n/**\n * @private\n * A constant factor used to compute the area of a polygon.\n * It's derived from the square of the Earth's radius divided by 2.\n *\n * @type {number}\n */\nconst FACTOR = (earthRadius * earthRadius) / 2;\n\n/**\n * @private\n * A constant used for converting degrees to radians.\n * Represents the ratio of PI to 180.\n *\n * @type {number}\n */\nconst PI_OVER_180 = Math.PI / 180;\n\n/**\n * @private\n * Calculate the approximate area of the polygon were it projected onto the earth.\n * Note that this area will be positive if ring is oriented clockwise, otherwise it will be negative.\n *\n * Reference:\n * Robert. G. Chamberlain and William H. Duquette, \"Some Algorithms for Polygons on a Sphere\",\n * JPL Publication 07-03, Jet Propulsion\n * Laboratory, Pasadena, CA, June 2007 https://trs.jpl.nasa.gov/handle/2014/40409\n *\n * @param {Array<Array<number>>} coords Ring Coordinates\n * @returns {number} The approximate signed geodesic area of the polygon in square meters.\n */\nfunction ringArea(coords: number[][]): number {\n  const coordsLength = coords.length - 1;\n\n  if (coordsLength <= 2) return 0;\n  let total = 0;\n\n  let i = 0;\n  while (i < coordsLength) {\n    const lower = coords[i];\n    const middle = coords[i + 1 === coordsLength ? 0 : i + 1];\n    const upper =\n      coords[i + 2 >= coordsLength ? (i + 2) % coordsLength : i + 2];\n\n    const lowerX = lower[0] * PI_OVER_180;\n    const middleY = middle[1] * PI_OVER_180;\n    const upperX = upper[0] * PI_OVER_180;\n\n    total += (upperX - lowerX) * Math.sin(middleY);\n\n    i++;\n  }\n\n  return total * FACTOR;\n}\n\nexport { area };\nexport default area;\n"]}