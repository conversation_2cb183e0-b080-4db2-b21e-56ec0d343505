{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { BBox, Feature, Polygon, GeoJsonProperties } from \"geojson\";\nimport { polygon, Id } from \"@turf/helpers\";\n\n/**\n * Takes a bbox and returns an equivalent {@link Polygon|polygon}.\n *\n * @function\n * @param {BBox} bbox extent in [minX, minY, maxX, maxY] order\n * @param {Object} [options={}] Optional parameters\n * @param {GeoJsonProperties} [options.properties={}] Translate properties to Polygon\n * @param {string|number} [options.id={}] Translate Id to Polygon\n * @returns {Feature<Polygon>} a Polygon representation of the bounding box\n * @example\n * var bbox = [0, 0, 10, 10];\n *\n * var poly = turf.bboxPolygon(bbox);\n *\n * //addToMap\n * var addToMap = [poly]\n */\nfunction bboxPolygon<P extends GeoJsonProperties = GeoJsonProperties>(\n  bbox: BBox,\n  options: {\n    properties?: P;\n    id?: Id;\n  } = {}\n): Feature<Polygon, P> {\n  // Convert BBox positions to Numbers\n  // No performance loss for including Number()\n  // https://github.com/Turfjs/turf/issues/1119\n  const west = Number(bbox[0]);\n  const south = Number(bbox[1]);\n  const east = Number(bbox[2]);\n  const north = Number(bbox[3]);\n\n  if (bbox.length === 6) {\n    throw new Error(\n      \"@turf/bbox-polygon does not support BBox with 6 positions\"\n    );\n  }\n\n  const lowLeft = [west, south];\n  const topLeft = [west, north];\n  const topRight = [east, north];\n  const lowRight = [east, south];\n\n  return polygon(\n    [[lowLeft, lowRight, topRight, topLeft, lowLeft]],\n    options.properties,\n    { bbox, id: options.id }\n  );\n}\n\nexport { bboxPolygon };\nexport default bboxPolygon;\n"], "mappings": ";AACA,SAAS,eAAmB;AAmB5B,SAAS,YACP,MACA,UAGI,CAAC,GACgB;AAIrB,QAAM,OAAO,OAAO,KAAK,CAAC,CAAC;AAC3B,QAAM,QAAQ,OAAO,KAAK,CAAC,CAAC;AAC5B,QAAM,OAAO,OAAO,KAAK,CAAC,CAAC;AAC3B,QAAM,QAAQ,OAAO,KAAK,CAAC,CAAC;AAE5B,MAAI,KAAK,WAAW,GAAG;AACrB,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,QAAM,UAAU,CAAC,MAAM,KAAK;AAC5B,QAAM,UAAU,CAAC,MAAM,KAAK;AAC5B,QAAM,WAAW,CAAC,MAAM,KAAK;AAC7B,QAAM,WAAW,CAAC,MAAM,KAAK;AAE7B,SAAO;AAAA,IACL,CAAC,CAAC,SAAS,UAAU,UAAU,SAAS,OAAO,CAAC;AAAA,IAChD,QAAQ;AAAA,IACR,EAAE,MAAM,IAAI,QAAQ,GAAG;AAAA,EACzB;AACF;AAGA,IAAO,4BAAQ;", "names": []}