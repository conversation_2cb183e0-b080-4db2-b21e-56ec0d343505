{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["// http://en.wikipedia.org/wiki/Haversine_formula\n// http://www.movable-type.co.uk/scripts/latlong.html\nimport { Feature, Point, GeoJsonProperties } from \"geojson\";\nimport {\n  Coord,\n  degreesToRadians,\n  lengthToRadians,\n  point,\n  radiansToDegrees,\n  Units,\n} from \"@turf/helpers\";\nimport { getCoord } from \"@turf/invariant\";\n\n/**\n * Takes a {@link Point} and calculates the location of a destination point given a distance in\n * degrees, radians, miles, or kilometers; and bearing in degrees.\n * This uses the [Haversine formula](http://en.wikipedia.org/wiki/Haversine_formula) to account for global curvature.\n *\n * @function\n * @param {Coord} origin starting point\n * @param {number} distance distance from the origin point\n * @param {number} bearing ranging from -180 to 180\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.units='kilometers'] miles, kilometers, degrees, or radians\n * @param {Object} [options.properties={}] Translate properties to Point\n * @returns {Feature<Point>} destination point\n * @example\n * var point = turf.point([-75.343, 39.984]);\n * var distance = 50;\n * var bearing = 90;\n * var options = {units: 'miles'};\n *\n * var destination = turf.destination(point, distance, bearing, options);\n *\n * //addToMap\n * var addToMap = [point, destination]\n * destination.properties['marker-color'] = '#f00';\n * point.properties['marker-color'] = '#0f0';\n */\nfunction destination<P extends GeoJsonProperties = GeoJsonProperties>(\n  origin: Coord,\n  distance: number,\n  bearing: number,\n  options: {\n    units?: Units;\n    properties?: P;\n  } = {}\n): Feature<Point, P> {\n  // Handle input\n  const coordinates1 = getCoord(origin);\n  const longitude1 = degreesToRadians(coordinates1[0]);\n  const latitude1 = degreesToRadians(coordinates1[1]);\n  const bearingRad = degreesToRadians(bearing);\n  const radians = lengthToRadians(distance, options.units);\n\n  // Main\n  const latitude2 = Math.asin(\n    Math.sin(latitude1) * Math.cos(radians) +\n      Math.cos(latitude1) * Math.sin(radians) * Math.cos(bearingRad)\n  );\n  const longitude2 =\n    longitude1 +\n    Math.atan2(\n      Math.sin(bearingRad) * Math.sin(radians) * Math.cos(latitude1),\n      Math.cos(radians) - Math.sin(latitude1) * Math.sin(latitude2)\n    );\n  const lng = radiansToDegrees(longitude2);\n  const lat = radiansToDegrees(latitude2);\n\n  return point([lng, lat], options.properties);\n}\n\nexport { destination };\nexport default destination;\n"], "mappings": ";AAGA;AAAA,EAEE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OAEK;AACP,SAAS,gBAAgB;AA4BzB,SAAS,YACP,QACA,UACA,SACA,UAGI,CAAC,GACc;AAEnB,QAAM,eAAe,SAAS,MAAM;AACpC,QAAM,aAAa,iBAAiB,aAAa,CAAC,CAAC;AACnD,QAAM,YAAY,iBAAiB,aAAa,CAAC,CAAC;AAClD,QAAM,aAAa,iBAAiB,OAAO;AAC3C,QAAM,UAAU,gBAAgB,UAAU,QAAQ,KAAK;AAGvD,QAAM,YAAY,KAAK;AAAA,IACrB,KAAK,IAAI,SAAS,IAAI,KAAK,IAAI,OAAO,IACpC,KAAK,IAAI,SAAS,IAAI,KAAK,IAAI,OAAO,IAAI,KAAK,IAAI,UAAU;AAAA,EACjE;AACA,QAAM,aACJ,aACA,KAAK;AAAA,IACH,KAAK,IAAI,UAAU,IAAI,KAAK,IAAI,OAAO,IAAI,KAAK,IAAI,SAAS;AAAA,IAC7D,KAAK,IAAI,OAAO,IAAI,KAAK,IAAI,SAAS,IAAI,KAAK,IAAI,SAAS;AAAA,EAC9D;AACF,QAAM,MAAM,iBAAiB,UAAU;AACvC,QAAM,MAAM,iBAAiB,SAAS;AAEtC,SAAO,MAAM,CAAC,KAAK,GAAG,GAAG,QAAQ,UAAU;AAC7C;AAGA,IAAO,2BAAQ;", "names": []}