"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _helpers = require('@turf/helpers');
var _invariant = require('@turf/invariant');
var _meta = require('@turf/meta');
function lineSegment(geojson) {
  if (!geojson) {
    throw new Error("geojson is required");
  }
  const results = [];
  _meta.flattenEach.call(void 0, geojson, (feature) => {
    lineSegmentFeature(feature, results);
  });
  return _helpers.featureCollection.call(void 0, results);
}
function lineSegmentFeature(geojson, results) {
  let coords = [];
  const geometry = geojson.geometry;
  if (geometry !== null) {
    switch (geometry.type) {
      case "Polygon":
        coords = _invariant.getCoords.call(void 0, geometry);
        break;
      case "LineString":
        coords = [_invariant.getCoords.call(void 0, geometry)];
    }
    coords.forEach((coord) => {
      const segments = createSegments(coord, geojson.properties);
      segments.forEach((segment) => {
        segment.id = results.length;
        results.push(segment);
      });
    });
  }
}
function createSegments(coords, properties) {
  const segments = [];
  coords.reduce((previousCoords, currentCoords) => {
    const segment = _helpers.lineString.call(void 0, [previousCoords, currentCoords], properties);
    segment.bbox = bbox(previousCoords, currentCoords);
    segments.push(segment);
    return currentCoords;
  });
  return segments;
}
function bbox(coords1, coords2) {
  const x1 = coords1[0];
  const y1 = coords1[1];
  const x2 = coords2[0];
  const y2 = coords2[1];
  const west = x1 < x2 ? x1 : x2;
  const south = y1 < y2 ? y1 : y2;
  const east = x1 > x2 ? x1 : x2;
  const north = y1 > y2 ? y1 : y2;
  return [west, south, east, north];
}
var turf_line_segment_default = lineSegment;



exports.default = turf_line_segment_default; exports.lineSegment = lineSegment;
//# sourceMappingURL=index.cjs.map