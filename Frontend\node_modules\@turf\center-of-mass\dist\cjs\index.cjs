"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _convex = require('@turf/convex');
var _centroid = require('@turf/centroid');
var _helpers = require('@turf/helpers');
var _invariant = require('@turf/invariant');
var _meta = require('@turf/meta');
function centerOfMass(geojson, options = {}) {
  switch (_invariant.getType.call(void 0, geojson)) {
    case "Point":
      return _helpers.point.call(void 0, _invariant.getCoord.call(void 0, geojson), options.properties);
    case "Polygon":
      var coords = [];
      _meta.coordEach.call(void 0, geojson, function(coord) {
        coords.push(coord);
      });
      var centre = _centroid.centroid.call(void 0, geojson, { properties: options.properties });
      var translation = centre.geometry.coordinates;
      var sx = 0;
      var sy = 0;
      var sArea = 0;
      var i, pi, pj, xi, xj, yi, yj, a;
      var neutralizedPoints = coords.map(function(point2) {
        return [point2[0] - translation[0], point2[1] - translation[1]];
      });
      for (i = 0; i < coords.length - 1; i++) {
        pi = neutralizedPoints[i];
        xi = pi[0];
        yi = pi[1];
        pj = neutralizedPoints[i + 1];
        xj = pj[0];
        yj = pj[1];
        a = xi * yj - xj * yi;
        sArea += a;
        sx += (xi + xj) * a;
        sy += (yi + yj) * a;
      }
      if (sArea === 0) {
        return centre;
      } else {
        var area = sArea * 0.5;
        var areaFactor = 1 / (6 * area);
        return _helpers.point.call(void 0, 
          [translation[0] + areaFactor * sx, translation[1] + areaFactor * sy],
          options.properties
        );
      }
    default:
      var hull = _convex.convex.call(void 0, geojson);
      if (hull) return centerOfMass(hull, { properties: options.properties });
      else return _centroid.centroid.call(void 0, geojson, { properties: options.properties });
  }
}
var turf_center_of_mass_default = centerOfMass;



exports.centerOfMass = centerOfMass; exports.default = turf_center_of_mass_default;
//# sourceMappingURL=index.cjs.map