{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-destination/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACGA;AAEE;AACA;AACA;AACA;AAAA,wCAEK;AACP,4CAAyB;AA4BzB,SAAS,WAAA,CACP,MAAA,EACA,QAAA,EACA,OAAA,EACA,QAAA,EAGI,CAAC,CAAA,EACc;AAEnB,EAAA,MAAM,aAAA,EAAe,iCAAA,MAAe,CAAA;AACpC,EAAA,MAAM,WAAA,EAAa,uCAAA,YAAiB,CAAa,CAAC,CAAC,CAAA;AACnD,EAAA,MAAM,UAAA,EAAY,uCAAA,YAAiB,CAAa,CAAC,CAAC,CAAA;AAClD,EAAA,MAAM,WAAA,EAAa,uCAAA,OAAwB,CAAA;AAC3C,EAAA,MAAM,QAAA,EAAU,sCAAA,QAAgB,EAAU,OAAA,CAAQ,KAAK,CAAA;AAGvD,EAAA,MAAM,UAAA,EAAY,IAAA,CAAK,IAAA;AAAA,IACrB,IAAA,CAAK,GAAA,CAAI,SAAS,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,OAAO,EAAA,EACpC,IAAA,CAAK,GAAA,CAAI,SAAS,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,OAAO,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,UAAU;AAAA,EACjE,CAAA;AACA,EAAA,MAAM,WAAA,EACJ,WAAA,EACA,IAAA,CAAK,KAAA;AAAA,IACH,IAAA,CAAK,GAAA,CAAI,UAAU,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,OAAO,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,SAAS,CAAA;AAAA,IAC7D,IAAA,CAAK,GAAA,CAAI,OAAO,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,SAAS,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,SAAS;AAAA,EAC9D,CAAA;AACF,EAAA,MAAM,IAAA,EAAM,uCAAA,UAA2B,CAAA;AACvC,EAAA,MAAM,IAAA,EAAM,uCAAA,SAA0B,CAAA;AAEtC,EAAA,OAAO,4BAAA,CAAO,GAAA,EAAK,GAAG,CAAA,EAAG,OAAA,CAAQ,UAAU,CAAA;AAC7C;AAGA,IAAO,yBAAA,EAAQ,WAAA;AD/Cf;AACE;AACA;AACF,8EAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-destination/dist/cjs/index.cjs", "sourcesContent": [null, "// http://en.wikipedia.org/wiki/Haversine_formula\n// http://www.movable-type.co.uk/scripts/latlong.html\nimport { Feature, Point, GeoJsonProperties } from \"geojson\";\nimport {\n  Coord,\n  degreesToRadians,\n  lengthToRadians,\n  point,\n  radiansToDegrees,\n  Units,\n} from \"@turf/helpers\";\nimport { getCoord } from \"@turf/invariant\";\n\n/**\n * Takes a {@link Point} and calculates the location of a destination point given a distance in\n * degrees, radians, miles, or kilometers; and bearing in degrees.\n * This uses the [Haversine formula](http://en.wikipedia.org/wiki/Haversine_formula) to account for global curvature.\n *\n * @function\n * @param {Coord} origin starting point\n * @param {number} distance distance from the origin point\n * @param {number} bearing ranging from -180 to 180\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.units='kilometers'] miles, kilometers, degrees, or radians\n * @param {Object} [options.properties={}] Translate properties to Point\n * @returns {Feature<Point>} destination point\n * @example\n * var point = turf.point([-75.343, 39.984]);\n * var distance = 50;\n * var bearing = 90;\n * var options = {units: 'miles'};\n *\n * var destination = turf.destination(point, distance, bearing, options);\n *\n * //addToMap\n * var addToMap = [point, destination]\n * destination.properties['marker-color'] = '#f00';\n * point.properties['marker-color'] = '#0f0';\n */\nfunction destination<P extends GeoJsonProperties = GeoJsonProperties>(\n  origin: Coord,\n  distance: number,\n  bearing: number,\n  options: {\n    units?: Units;\n    properties?: P;\n  } = {}\n): Feature<Point, P> {\n  // Handle input\n  const coordinates1 = getCoord(origin);\n  const longitude1 = degreesToRadians(coordinates1[0]);\n  const latitude1 = degreesToRadians(coordinates1[1]);\n  const bearingRad = degreesToRadians(bearing);\n  const radians = lengthToRadians(distance, options.units);\n\n  // Main\n  const latitude2 = Math.asin(\n    Math.sin(latitude1) * Math.cos(radians) +\n      Math.cos(latitude1) * Math.sin(radians) * Math.cos(bearingRad)\n  );\n  const longitude2 =\n    longitude1 +\n    Math.atan2(\n      Math.sin(bearingRad) * Math.sin(radians) * Math.cos(latitude1),\n      Math.cos(radians) - Math.sin(latitude1) * Math.sin(latitude2)\n    );\n  const lng = radiansToDegrees(longitude2);\n  const lat = radiansToDegrees(latitude2);\n\n  return point([lng, lat], options.properties);\n}\n\nexport { destination };\nexport default destination;\n"]}