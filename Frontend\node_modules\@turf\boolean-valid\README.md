# @turf/boolean-valid

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## booleanValid

booleanValid checks if the geometry is a valid according to the OGC Simple Feature Specification.

### Parameters

*   `feature` **([Geometry][1] | [Feature][2]\<any>)** GeoJSON Feature or Geometry

### Examples

```javascript
var line = turf.lineString([[1, 1], [1, 2], [1, 3], [1, 4]]);

turf.booleanValid(line); // => true
turf.booleanValid({foo: "bar"}); // => false
```

Returns **[boolean][3]** true/false

[1]: https://tools.ietf.org/html/rfc7946#section-3.1

[2]: https://tools.ietf.org/html/rfc7946#section-3.2

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/boolean-valid
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
