// index.ts
import { bearing } from "@turf/bearing";
import { bearingToAzimuth, isObject } from "@turf/helpers";
import { rhumbBearing } from "@turf/rhumb-bearing";
function angle(startPoint, midPoint, endPoint, options = {}) {
  if (!isObject(options)) {
    throw new Error("options is invalid");
  }
  if (!startPoint) {
    throw new Error("startPoint is required");
  }
  if (!midPoint) {
    throw new Error("midPoint is required");
  }
  if (!endPoint) {
    throw new Error("endPoint is required");
  }
  const A = startPoint;
  const O = midPoint;
  const B = endPoint;
  const azimuthOA = bearingToAzimuth(
    options.mercator !== true ? bearing(O, A) : rhumbBearing(O, A)
  );
  let azimuthOB = bearingToAzimuth(
    options.mercator !== true ? bearing(O, B) : rhumbBearing(O, B)
  );
  if (azimuthOB < azimuthOA) {
    azimuthOB = azimuthOB + 360;
  }
  const angleAOB = azimuthOB - azimuthOA;
  if (options.explementary === true) {
    return 360 - angleAOB;
  }
  return angleAOB;
}
var turf_angle_default = angle;
export {
  angle,
  turf_angle_default as default
};
//# sourceMappingURL=index.js.map