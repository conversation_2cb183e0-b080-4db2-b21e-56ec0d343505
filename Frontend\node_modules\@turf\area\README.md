# @turf/area

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## area

Calculates the geodesic area in square meters of one or more polygons.

### Parameters

*   `geojson` **[GeoJSON][1]** input polygon(s) as [Geometry][2], [Feature][3], or [FeatureCollection][4]

### Examples

```javascript
var polygon = turf.polygon([[[125, -15], [113, -22], [154, -27], [144, -15], [125, -15]]]);

var area = turf.area(polygon);

//addToMap
var addToMap = [polygon]
polygon.properties.area = area
```

Returns **[number][5]** area in square meters

[1]: https://tools.ietf.org/html/rfc7946#section-3

[2]: https://tools.ietf.org/html/rfc7946#section-3.1

[3]: https://tools.ietf.org/html/rfc7946#section-3.2

[4]: https://tools.ietf.org/html/rfc7946#section-3.3

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/area
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
