{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Feature, GeoJsonProperties } from \"geojson\";\nimport { AllGeoJSON } from \"@turf/helpers\";\n\n/**\n * Returns a cloned copy of the passed GeoJSON Object, including possible 'Foreign Members'.\n * ~3-5x faster than the common JSON.parse + JSON.stringify combo method.\n *\n * @function\n * @param {GeoJSON} geojson GeoJSON Object\n * @returns {GeoJSON} cloned GeoJSON Object\n * @example\n * var line = turf.lineString([[-74, 40], [-78, 42], [-82, 35]], {color: 'red'});\n *\n * var lineCloned = turf.clone(line);\n */\nfunction clone<T extends AllGeoJSON>(geojson: T): T {\n  if (!geojson) {\n    throw new Error(\"geojson is required\");\n  }\n\n  switch (geojson.type) {\n    case \"Feature\":\n      return cloneFeature(geojson);\n    case \"FeatureCollection\":\n      return cloneFeatureCollection(geojson);\n    case \"Point\":\n    case \"LineString\":\n    case \"Polygon\":\n    case \"MultiPoint\":\n    case \"MultiLineString\":\n    case \"MultiPolygon\":\n    case \"GeometryCollection\":\n      return cloneGeometry(geojson);\n    default:\n      throw new Error(\"unknown GeoJSON type\");\n  }\n}\n\n/**\n * Clone Feature\n *\n * @private\n * @param {Feature<any>} geojson GeoJSON Feature\n * @returns {Feature<any>} cloned Feature\n */\nfunction cloneFeature(geojson: any) {\n  const cloned: any = { type: \"Feature\" };\n  // Preserve Foreign Members\n  Object.keys(geojson).forEach((key) => {\n    switch (key) {\n      case \"type\":\n      case \"properties\":\n      case \"geometry\":\n        return;\n      default:\n        cloned[key] = geojson[key];\n    }\n  });\n  // Add properties & geometry last\n  cloned.properties = cloneProperties(geojson.properties);\n  if (geojson.geometry == null) {\n    cloned.geometry = null;\n  } else {\n    cloned.geometry = cloneGeometry(geojson.geometry);\n  }\n  return cloned;\n}\n\n/**\n * Clone Properties\n *\n * @private\n * @param {Object} properties GeoJSON Properties\n * @returns {Object} cloned Properties\n */\nfunction cloneProperties(properties: GeoJsonProperties) {\n  const cloned: { [key: string]: any } = {};\n  if (!properties) {\n    return cloned;\n  }\n  Object.keys(properties).forEach((key) => {\n    const value = properties[key];\n    if (typeof value === \"object\") {\n      if (value === null) {\n        // handle null\n        cloned[key] = null;\n      } else if (Array.isArray(value)) {\n        // handle Array\n        cloned[key] = value.map((item) => {\n          return item;\n        });\n      } else {\n        // handle generic Object\n        cloned[key] = cloneProperties(value);\n      }\n    } else {\n      cloned[key] = value;\n    }\n  });\n  return cloned;\n}\n\n/**\n * Clone Feature Collection\n *\n * @private\n * @param {FeatureCollection<any>} geojson GeoJSON Feature Collection\n * @returns {FeatureCollection<any>} cloned Feature Collection\n */\nfunction cloneFeatureCollection(geojson: any) {\n  const cloned: any = { type: \"FeatureCollection\" };\n\n  // Preserve Foreign Members\n  Object.keys(geojson).forEach((key) => {\n    switch (key) {\n      case \"type\":\n      case \"features\":\n        return;\n      default:\n        cloned[key] = geojson[key];\n    }\n  });\n  // Add features\n  cloned.features = geojson.features.map((feature: Feature<any>) => {\n    return cloneFeature(feature);\n  });\n  return cloned;\n}\n\n/**\n * Clone Geometry\n *\n * @private\n * @param {Geometry<any>} geometry GeoJSON Geometry\n * @returns {Geometry<any>} cloned Geometry\n */\nfunction cloneGeometry(geometry: any) {\n  const geom: any = { type: geometry.type };\n  if (geometry.bbox) {\n    geom.bbox = geometry.bbox;\n  }\n\n  if (geometry.type === \"GeometryCollection\") {\n    geom.geometries = geometry.geometries.map((g: any) => {\n      return cloneGeometry(g);\n    });\n    return geom;\n  }\n  geom.coordinates = deepSlice(geometry.coordinates);\n  return geom;\n}\n\n/**\n * Deep Slice coordinates\n *\n * @private\n * @param {Coordinates} coords Coordinates\n * @returns {Coordinates} all coordinates sliced\n */\nfunction deepSlice<C = any[]>(coords: C): C {\n  const cloned: any = coords;\n  if (typeof cloned[0] !== \"object\") {\n    return cloned.slice();\n  }\n  return cloned.map((coord: any) => {\n    return deepSlice(coord);\n  });\n}\n\nexport { clone, cloneProperties };\nexport default clone;\n"], "mappings": ";AAeA,SAAS,MAA4B,SAAe;AAClD,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,qBAAqB;AAAA,EACvC;AAEA,UAAQ,QAAQ,MAAM;AAAA,IACpB,KAAK;AACH,aAAO,aAAa,OAAO;AAAA,IAC7B,KAAK;AACH,aAAO,uBAAuB,OAAO;AAAA,IACvC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,cAAc,OAAO;AAAA,IAC9B;AACE,YAAM,IAAI,MAAM,sBAAsB;AAAA,EAC1C;AACF;AASA,SAAS,aAAa,SAAc;AAClC,QAAM,SAAc,EAAE,MAAM,UAAU;AAEtC,SAAO,KAAK,OAAO,EAAE,QAAQ,CAAC,QAAQ;AACpC,YAAQ,KAAK;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH;AAAA,MACF;AACE,eAAO,GAAG,IAAI,QAAQ,GAAG;AAAA,IAC7B;AAAA,EACF,CAAC;AAED,SAAO,aAAa,gBAAgB,QAAQ,UAAU;AACtD,MAAI,QAAQ,YAAY,MAAM;AAC5B,WAAO,WAAW;AAAA,EACpB,OAAO;AACL,WAAO,WAAW,cAAc,QAAQ,QAAQ;AAAA,EAClD;AACA,SAAO;AACT;AASA,SAAS,gBAAgB,YAA+B;AACtD,QAAM,SAAiC,CAAC;AACxC,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AACA,SAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,QAAQ;AACvC,UAAM,QAAQ,WAAW,GAAG;AAC5B,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,UAAU,MAAM;AAElB,eAAO,GAAG,IAAI;AAAA,MAChB,WAAW,MAAM,QAAQ,KAAK,GAAG;AAE/B,eAAO,GAAG,IAAI,MAAM,IAAI,CAAC,SAAS;AAChC,iBAAO;AAAA,QACT,CAAC;AAAA,MACH,OAAO;AAEL,eAAO,GAAG,IAAI,gBAAgB,KAAK;AAAA,MACrC;AAAA,IACF,OAAO;AACL,aAAO,GAAG,IAAI;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AASA,SAAS,uBAAuB,SAAc;AAC5C,QAAM,SAAc,EAAE,MAAM,oBAAoB;AAGhD,SAAO,KAAK,OAAO,EAAE,QAAQ,CAAC,QAAQ;AACpC,YAAQ,KAAK;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACH;AAAA,MACF;AACE,eAAO,GAAG,IAAI,QAAQ,GAAG;AAAA,IAC7B;AAAA,EACF,CAAC;AAED,SAAO,WAAW,QAAQ,SAAS,IAAI,CAAC,YAA0B;AAChE,WAAO,aAAa,OAAO;AAAA,EAC7B,CAAC;AACD,SAAO;AACT;AASA,SAAS,cAAc,UAAe;AACpC,QAAM,OAAY,EAAE,MAAM,SAAS,KAAK;AACxC,MAAI,SAAS,MAAM;AACjB,SAAK,OAAO,SAAS;AAAA,EACvB;AAEA,MAAI,SAAS,SAAS,sBAAsB;AAC1C,SAAK,aAAa,SAAS,WAAW,IAAI,CAAC,MAAW;AACpD,aAAO,cAAc,CAAC;AAAA,IACxB,CAAC;AACD,WAAO;AAAA,EACT;AACA,OAAK,cAAc,UAAU,SAAS,WAAW;AACjD,SAAO;AACT;AASA,SAAS,UAAqB,QAAc;AAC1C,QAAM,SAAc;AACpB,MAAI,OAAO,OAAO,CAAC,MAAM,UAAU;AACjC,WAAO,OAAO,MAAM;AAAA,EACtB;AACA,SAAO,OAAO,IAAI,CAAC,UAAe;AAChC,WAAO,UAAU,KAAK;AAAA,EACxB,CAAC;AACH;AAGA,IAAO,qBAAQ;", "names": []}