{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import {\n  Feature,\n  FeatureCollection,\n  GeoJsonProperties,\n  GeometryObject,\n} from \"geojson\";\nimport { featureEach } from \"@turf/meta\";\nimport { featureCollection } from \"@turf/helpers\";\n\n/**\n * Get Cluster\n *\n * @function\n * @param {FeatureCollection} geojson GeoJSON Features\n * @param {*} filter Filter used on GeoJSON properties to get Cluster\n * @returns {FeatureCollection} Single Cluster filtered by GeoJSON Properties\n * @example\n * var geojson = turf.featureCollection([\n *     turf.point([0, 0], {'marker-symbol': 'circle'}),\n *     turf.point([2, 4], {'marker-symbol': 'star'}),\n *     turf.point([3, 6], {'marker-symbol': 'star'}),\n *     turf.point([5, 1], {'marker-symbol': 'square'}),\n *     turf.point([4, 2], {'marker-symbol': 'circle'})\n * ]);\n *\n * // Create a cluster using K-Means (adds `cluster` to GeoJSON properties)\n * var clustered = turf.clustersKmeans(geojson);\n *\n * // Retrieve first cluster (0)\n * var cluster = turf.getCluster(clustered, {cluster: 0});\n * //= cluster\n *\n * // Retrieve cluster based on custom properties\n * turf.getCluster(clustered, {'marker-symbol': 'circle'}).length;\n * //= 2\n * turf.getCluster(clustered, {'marker-symbol': 'square'}).length;\n * //= 1\n */\nfunction getCluster<\n  G extends GeometryObject,\n  P extends GeoJsonProperties = GeoJsonProperties,\n>(geojson: FeatureCollection<G, P>, filter: any): FeatureCollection<G, P> {\n  // Validation\n  if (!geojson) throw new Error(\"geojson is required\");\n  if (geojson.type !== \"FeatureCollection\")\n    throw new Error(\"geojson must be a FeatureCollection\");\n  if (filter === undefined || filter === null)\n    throw new Error(\"filter is required\");\n\n  // Filter Features\n  var features: Feature<G, P>[] = [];\n  featureEach<G, P>(geojson, function (feature) {\n    if (applyFilter(feature.properties, filter)) features.push(feature);\n  });\n  return featureCollection(features);\n}\n\n/**\n * Callback for clusterEach\n *\n * @callback clusterEachCallback\n * @param {FeatureCollection} [cluster] The current cluster being processed.\n * @param {any} [clusterValue] Value used to create cluster being processed.\n * @param {number} [currentIndex] The index of the current element being processed in the array.Starts at index 0\n * @returns {void}\n */\n\n/**\n * clusterEach\n *\n * @function\n * @param {FeatureCollection} geojson GeoJSON Features\n * @param {string|number} property GeoJSON property key/value used to create clusters\n * @param {clusterEachCallback} callback a method that takes (cluster, clusterValue, currentIndex)\n * @returns {void}\n * @example\n * var geojson = turf.featureCollection([\n *     turf.point([0, 0]),\n *     turf.point([2, 4]),\n *     turf.point([3, 6]),\n *     turf.point([5, 1]),\n *     turf.point([4, 2])\n * ]);\n *\n * // Create a cluster using K-Means (adds `cluster` to GeoJSON properties)\n * var clustered = turf.clustersKmeans(geojson);\n *\n * // Iterate over each cluster\n * turf.clusterEach(clustered, 'cluster', function (cluster, clusterValue, currentIndex) {\n *     //= cluster\n *     //= clusterValue\n *     //= currentIndex\n * })\n *\n * // Calculate the total number of clusters\n * var total = 0\n * turf.clusterEach(clustered, 'cluster', function () {\n *     total++;\n * });\n *\n * // Create an Array of all the values retrieved from the 'cluster' property\n * var values = []\n * turf.clusterEach(clustered, 'cluster', function (cluster, clusterValue) {\n *     values.push(clusterValue);\n * });\n */\nfunction clusterEach<\n  G extends GeometryObject,\n  P extends GeoJsonProperties = GeoJsonProperties,\n>(\n  geojson: FeatureCollection<G, P>,\n  property: number | string,\n  callback: (\n    cluster: FeatureCollection<G, P>,\n    clusterValue?: any,\n    currentIndex?: number\n  ) => void\n): void {\n  // Validation\n  if (!geojson) throw new Error(\"geojson is required\");\n  if (geojson.type !== \"FeatureCollection\")\n    throw new Error(\"geojson must be a FeatureCollection\");\n  if (property === undefined || property === null)\n    throw new Error(\"property is required\");\n\n  // Create clusters based on property values\n  var bins = createBins(geojson, property);\n  var values = Object.keys(bins);\n  for (var index = 0; index < values.length; index++) {\n    var value = values[index];\n    var bin = bins[value];\n    var features = [];\n    for (var i = 0; i < bin.length; i++) {\n      features.push(geojson.features[bin[i]]);\n    }\n    callback(featureCollection(features), value, index);\n  }\n}\n\n/**\n * Callback for clusterReduce\n *\n * The first time the callback function is called, the values provided as arguments depend\n * on whether the reduce method has an initialValue argument.\n *\n * If an initialValue is provided to the reduce method:\n *  - The previousValue argument is initialValue.\n *  - The currentValue argument is the value of the first element present in the array.\n *\n * If an initialValue is not provided:\n *  - The previousValue argument is the value of the first element present in the array.\n *  - The currentValue argument is the value of the second element present in the array.\n *\n * @callback clusterReduceCallback\n * @param {*} [previousValue] The accumulated value previously returned in the last invocation\n * of the callback, or initialValue, if supplied.\n * @param {FeatureCollection} [cluster] The current cluster being processed.\n * @param {*} [clusterValue] Value used to create cluster being processed.\n * @param {number} [currentIndex] The index of the current element being processed in the\n * array. Starts at index 0, if an initialValue is provided, and at index 1 otherwise.\n * @returns {void}\n */\n\n/**\n * Reduce clusters in GeoJSON Features, similar to Array.reduce()\n *\n * @function\n * @param {FeatureCollection} geojson GeoJSON Features\n * @param {string|number} property GeoJSON property key/value used to create clusters\n * @param {clusterReduceCallback} callback a method that takes (previousValue, cluster, clusterValue, currentIndex)\n * @param {any} [initialValue] Value to use as the first argument to the first call of the callback.\n * @returns {any} The value that results from the reduction.\n * @example\n * var geojson = turf.featureCollection([\n *     turf.point([0, 0]),\n *     turf.point([2, 4]),\n *     turf.point([3, 6]),\n *     turf.point([5, 1]),\n *     turf.point([4, 2])\n * ]);\n *\n * // Create a cluster using K-Means (adds `cluster` to GeoJSON properties)\n * var clustered = turf.clustersKmeans(geojson);\n *\n * // Iterate over each cluster and perform a calculation\n * var initialValue = 0\n * turf.clusterReduce(clustered, 'cluster', function (previousValue, cluster, clusterValue, currentIndex) {\n *     //=previousValue\n *     //=cluster\n *     //=clusterValue\n *     //=currentIndex\n *     return previousValue++;\n * }, initialValue);\n *\n * // Calculate the total number of clusters\n * var total = turf.clusterReduce(clustered, 'cluster', function (previousValue) {\n *     return previousValue++;\n * }, 0);\n *\n * // Create an Array of all the values retrieved from the 'cluster' property\n * var values = turf.clusterReduce(clustered, 'cluster', function (previousValue, cluster, clusterValue) {\n *     return previousValue.concat(clusterValue);\n * }, []);\n */\nfunction clusterReduce<\n  G extends GeometryObject,\n  P extends GeoJsonProperties = GeoJsonProperties,\n>(\n  geojson: FeatureCollection<G, P>,\n  property: number | string,\n  callback: (\n    previousValue: any | undefined,\n    cluster: FeatureCollection<G, P>,\n    clusterValue?: any,\n    currentIndex?: number\n  ) => void,\n  initialValue?: any\n): void {\n  var previousValue = initialValue;\n  clusterEach(\n    geojson,\n    property,\n    function (cluster, clusterValue, currentIndex) {\n      if (currentIndex === 0 && initialValue === undefined)\n        previousValue = cluster;\n      else\n        previousValue = callback(\n          previousValue,\n          cluster,\n          clusterValue,\n          currentIndex\n        );\n    }\n  );\n  return previousValue;\n}\n\n/**\n * Create Bins\n *\n * @private\n * @param {FeatureCollection} geojson GeoJSON Features\n * @param {string|number} property Property values are used to create bins\n * @returns {Object} bins with Feature IDs\n * @example\n * var geojson = turf.featureCollection([\n *     turf.point([0, 0], {cluster: 0, foo: 'null'}),\n *     turf.point([2, 4], {cluster: 1, foo: 'bar'}),\n *     turf.point([5, 1], {0: 'foo'}),\n *     turf.point([3, 6], {cluster: 1}),\n * ]);\n * createBins(geojson, 'cluster');\n * //= { '0': [ 0 ], '1': [ 1, 3 ] }\n */\nfunction createBins(\n  geojson: FeatureCollection<any>,\n  property: string | number\n) {\n  var bins: Record<string, number[]> = {};\n\n  featureEach(geojson, function (feature, i) {\n    var properties = feature.properties || {};\n    if (Object.prototype.hasOwnProperty.call(properties, String(property))) {\n      var value = properties[property];\n      if (Object.prototype.hasOwnProperty.call(bins, value))\n        bins[value].push(i);\n      else bins[value] = [i];\n    }\n  });\n  return bins;\n}\n\n/**\n * Apply Filter\n *\n * @private\n * @param {*} properties Properties\n * @param {*} filter Filter\n * @returns {boolean} applied Filter to properties\n */\nfunction applyFilter(properties: any, filter: any) {\n  if (properties === undefined) return false;\n  var filterType = typeof filter;\n\n  // String & Number\n  if (filterType === \"number\" || filterType === \"string\")\n    return Object.prototype.hasOwnProperty.call(properties, filter);\n  // Array\n  else if (Array.isArray(filter)) {\n    for (var i = 0; i < filter.length; i++) {\n      if (!applyFilter(properties, filter[i])) return false;\n    }\n    return true;\n    // Object\n  } else {\n    return propertiesContainsFilter(properties, filter);\n  }\n}\n\n/**\n * Properties contains filter (does not apply deepEqual operations)\n *\n * @private\n * @param {*} properties Properties\n * @param {Object} filter Filter\n * @returns {boolean} does filter equal Properties\n * @example\n * propertiesContainsFilter({foo: 'bar', cluster: 0}, {cluster: 0})\n * //= true\n * propertiesContainsFilter({foo: 'bar', cluster: 0}, {cluster: 1})\n * //= false\n */\nfunction propertiesContainsFilter(properties: any, filter: any): boolean {\n  var keys = Object.keys(filter);\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    if (properties[key] !== filter[key]) return false;\n  }\n  return true;\n}\n\n/**\n * Filter Properties\n *\n * @private\n * @param {*} properties Properties\n * @param {Array<string>} keys Used to filter Properties\n * @returns {*} filtered Properties\n * @example\n * filterProperties({foo: 'bar', cluster: 0}, ['cluster'])\n * //= {cluster: 0}\n */\nfunction filterProperties(\n  properties: Record<string, any>,\n  keys: string[]\n): any {\n  if (!keys) return {};\n  if (!keys.length) return {};\n\n  var newProperties: Record<string, any> = {};\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    if (Object.prototype.hasOwnProperty.call(properties, key))\n      newProperties[key] = properties[key];\n  }\n  return newProperties;\n}\n\nexport {\n  getCluster,\n  clusterEach,\n  clusterReduce,\n  createBins,\n  applyFilter,\n  propertiesContainsFilter,\n  filterProperties,\n};\n// No default export!\n"], "mappings": ";AAMA,SAAS,mBAAmB;AAC5B,SAAS,yBAAyB;AA+BlC,SAAS,WAGP,SAAkC,QAAsC;AAExE,MAAI,CAAC,QAAS,OAAM,IAAI,MAAM,qBAAqB;AACnD,MAAI,QAAQ,SAAS;AACnB,UAAM,IAAI,MAAM,qCAAqC;AACvD,MAAI,WAAW,UAAa,WAAW;AACrC,UAAM,IAAI,MAAM,oBAAoB;AAGtC,MAAI,WAA4B,CAAC;AACjC,cAAkB,SAAS,SAAU,SAAS;AAC5C,QAAI,YAAY,QAAQ,YAAY,MAAM,EAAG,UAAS,KAAK,OAAO;AAAA,EACpE,CAAC;AACD,SAAO,kBAAkB,QAAQ;AACnC;AAmDA,SAAS,YAIP,SACA,UACA,UAKM;AAEN,MAAI,CAAC,QAAS,OAAM,IAAI,MAAM,qBAAqB;AACnD,MAAI,QAAQ,SAAS;AACnB,UAAM,IAAI,MAAM,qCAAqC;AACvD,MAAI,aAAa,UAAa,aAAa;AACzC,UAAM,IAAI,MAAM,sBAAsB;AAGxC,MAAI,OAAO,WAAW,SAAS,QAAQ;AACvC,MAAI,SAAS,OAAO,KAAK,IAAI;AAC7B,WAAS,QAAQ,GAAG,QAAQ,OAAO,QAAQ,SAAS;AAClD,QAAI,QAAQ,OAAO,KAAK;AACxB,QAAI,MAAM,KAAK,KAAK;AACpB,QAAI,WAAW,CAAC;AAChB,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,eAAS,KAAK,QAAQ,SAAS,IAAI,CAAC,CAAC,CAAC;AAAA,IACxC;AACA,aAAS,kBAAkB,QAAQ,GAAG,OAAO,KAAK;AAAA,EACpD;AACF;AAmEA,SAAS,cAIP,SACA,UACA,UAMA,cACM;AACN,MAAI,gBAAgB;AACpB;AAAA,IACE;AAAA,IACA;AAAA,IACA,SAAU,SAAS,cAAc,cAAc;AAC7C,UAAI,iBAAiB,KAAK,iBAAiB;AACzC,wBAAgB;AAAA;AAEhB,wBAAgB;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,IACJ;AAAA,EACF;AACA,SAAO;AACT;AAmBA,SAAS,WACP,SACA,UACA;AACA,MAAI,OAAiC,CAAC;AAEtC,cAAY,SAAS,SAAU,SAAS,GAAG;AACzC,QAAI,aAAa,QAAQ,cAAc,CAAC;AACxC,QAAI,OAAO,UAAU,eAAe,KAAK,YAAY,OAAO,QAAQ,CAAC,GAAG;AACtE,UAAI,QAAQ,WAAW,QAAQ;AAC/B,UAAI,OAAO,UAAU,eAAe,KAAK,MAAM,KAAK;AAClD,aAAK,KAAK,EAAE,KAAK,CAAC;AAAA,UACf,MAAK,KAAK,IAAI,CAAC,CAAC;AAAA,IACvB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAUA,SAAS,YAAY,YAAiB,QAAa;AACjD,MAAI,eAAe,OAAW,QAAO;AACrC,MAAI,aAAa,OAAO;AAGxB,MAAI,eAAe,YAAY,eAAe;AAC5C,WAAO,OAAO,UAAU,eAAe,KAAK,YAAY,MAAM;AAAA,WAEvD,MAAM,QAAQ,MAAM,GAAG;AAC9B,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAI,CAAC,YAAY,YAAY,OAAO,CAAC,CAAC,EAAG,QAAO;AAAA,IAClD;AACA,WAAO;AAAA,EAET,OAAO;AACL,WAAO,yBAAyB,YAAY,MAAM;AAAA,EACpD;AACF;AAeA,SAAS,yBAAyB,YAAiB,QAAsB;AACvE,MAAI,OAAO,OAAO,KAAK,MAAM;AAC7B,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,QAAI,MAAM,KAAK,CAAC;AAChB,QAAI,WAAW,GAAG,MAAM,OAAO,GAAG,EAAG,QAAO;AAAA,EAC9C;AACA,SAAO;AACT;AAaA,SAAS,iBACP,YACA,MACK;AACL,MAAI,CAAC,KAAM,QAAO,CAAC;AACnB,MAAI,CAAC,KAAK,OAAQ,QAAO,CAAC;AAE1B,MAAI,gBAAqC,CAAC;AAC1C,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,QAAI,MAAM,KAAK,CAAC;AAChB,QAAI,OAAO,UAAU,eAAe,KAAK,YAAY,GAAG;AACtD,oBAAc,GAAG,IAAI,WAAW,GAAG;AAAA,EACvC;AACA,SAAO;AACT;", "names": []}