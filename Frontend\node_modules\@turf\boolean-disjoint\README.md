# @turf/boolean-disjoint

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## booleanDisjoint

Boolean-disjoint returns (TRUE) if the intersection of the two geometries is an empty set.

### Parameters

*   `feature1` **([Geometry][1] | [Feature][2]\<any>)** GeoJSON Feature or Geometry
*   `feature2` **([Geometry][1] | [Feature][2]\<any>)** GeoJSON Feature or Geometry
*   `options` **[Object][3]** Optional parameters (optional, default `{}`)

    *   `options.ignoreSelfIntersections` **[boolean][4]** ignore self-intersections on input features (optional, default `true`)

### Examples

```javascript
var point = turf.point([2, 2]);
var line = turf.lineString([[1, 1], [1, 2], [1, 3], [1, 4]]);

turf.booleanDisjoint(line, point);
//=true
```

Returns **[boolean][4]** true if the intersection is an empty set, false otherwise

[1]: https://tools.ietf.org/html/rfc7946#section-3.1

[2]: https://tools.ietf.org/html/rfc7946#section-3.2

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/boolean-disjoint
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```


### Diagrams

![esri-disjoint](diagrams/esri-disjoint.gif)