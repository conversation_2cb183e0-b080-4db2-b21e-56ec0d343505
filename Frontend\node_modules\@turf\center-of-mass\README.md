# @turf/center-of-mass

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## centerOfMass

Takes any [Feature][1] or a [FeatureCollection][2] and returns its [center of mass][3] using this formula: [Centroid of Polygon][4].

### Parameters

*   `geojson` **[GeoJSON][5]** GeoJSON to be centered
*   `options` **[Object][6]** Optional Parameters (optional, default `{}`)

    *   `options.properties` **[Object][6]** Translate Properties to Feature (optional, default `{}`)

### Examples

```javascript
var polygon = turf.polygon([[[-81, 41], [-88, 36], [-84, 31], [-80, 33], [-77, 39], [-81, 41]]]);

var center = turf.centerOfMass(polygon);

//addToMap
var addToMap = [polygon, center]
```

Returns **[Feature][1]<[Point][7]>** the center of mass

[1]: https://tools.ietf.org/html/rfc7946#section-3.2

[2]: https://tools.ietf.org/html/rfc7946#section-3.3

[3]: https://en.wikipedia.org/wiki/Center_of_mass

[4]: https://en.wikipedia.org/wiki/Centroid#Centroid_of_polygon

[5]: https://tools.ietf.org/html/rfc7946#section-3

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[7]: https://tools.ietf.org/html/rfc7946#section-3.1.2

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/center-of-mass
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
