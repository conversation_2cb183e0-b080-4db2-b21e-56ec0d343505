import { GeoJsonProperties, Point, Feature, Polygon } from 'geojson';
import { Units } from '@turf/helpers';

/**
 * Takes a {@link Point} and calculates the circle polygon given a radius in degrees, radians, miles, or kilometers; and steps for precision.
 *
 * @function
 * @param {Feature<Point>|number[]} center center point
 * @param {number} radius radius of the circle
 * @param {Object} [options={}] Optional parameters
 * @param {number} [options.steps=64] number of steps
 * @param {string} [options.units='kilometers'] miles, kilometers, degrees, or radians
 * @param {Object} [options.properties={}] properties
 * @returns {Feature<Polygon>} circle polygon
 * @example
 * var center = [-75.343, 39.984];
 * var radius = 5;
 * var options = {steps: 10, units: 'kilometers', properties: {foo: 'bar'}};
 * var circle = turf.circle(center, radius, options);
 *
 * //addToMap
 * var addToMap = [turf.point(center), circle]
 */
declare function circle<P extends GeoJsonProperties = GeoJsonProperties>(center: number[] | Point | Feature<Point, P>, radius: number, options?: {
    steps?: number;
    units?: Units;
    properties?: P;
}): Feature<Polygon, P>;

export { circle, circle as default };
