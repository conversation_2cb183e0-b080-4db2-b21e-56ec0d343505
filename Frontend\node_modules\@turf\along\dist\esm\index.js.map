{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Feature, LineString, Point } from \"geojson\";\nimport { bearing } from \"@turf/bearing\";\nimport { destination } from \"@turf/destination\";\nimport { distance as measureDistance } from \"@turf/distance\";\nimport { point, Units } from \"@turf/helpers\";\nimport { getGeom } from \"@turf/invariant\";\n\n/**\n * Takes a {@link LineString} and returns a {@link Point} at a specified distance along the line.\n *\n * @function\n * @param {Feature<LineString>|LineString} line input line\n * @param {number} distance distance along the line\n * @param {Object} [options] Optional parameters\n * @param {Units} [options.units=\"kilometers\"] can be degrees, radians, miles, or kilometers\n * @returns {Feature<Point>} Point `distance` `units` along the line\n * @example\n * var line = turf.lineString([[-83, 30], [-84, 36], [-78, 41]]);\n * var options = {units: 'miles'};\n *\n * var along = turf.along(line, 200, options);\n *\n * //addToMap\n * var addToMap = [along, line]\n */\nfunction along(\n  line: Feature<LineString> | LineString,\n  distance: number,\n  options: { units?: Units } = {}\n): Feature<Point> {\n  // Get Coords\n  const geom = getGeom(line);\n  const coords = geom.coordinates;\n  let travelled = 0;\n  for (let i = 0; i < coords.length; i++) {\n    if (distance >= travelled && i === coords.length - 1) {\n      break;\n    } else if (travelled >= distance) {\n      const overshot = distance - travelled;\n      if (!overshot) {\n        return point(coords[i]);\n      } else {\n        const direction = bearing(coords[i], coords[i - 1]) - 180;\n        const interpolated = destination(\n          coords[i],\n          overshot,\n          direction,\n          options\n        );\n        return interpolated;\n      }\n    } else {\n      travelled += measureDistance(coords[i], coords[i + 1], options);\n    }\n  }\n  return point(coords[coords.length - 1]);\n}\n\nexport { along };\nexport default along;\n"], "mappings": ";AACA,SAAS,eAAe;AACxB,SAAS,mBAAmB;AAC5B,SAAS,YAAY,uBAAuB;AAC5C,SAAS,aAAoB;AAC7B,SAAS,eAAe;AAoBxB,SAAS,MACP,MACA,UACA,UAA6B,CAAC,GACd;AAEhB,QAAM,OAAO,QAAQ,IAAI;AACzB,QAAM,SAAS,KAAK;AACpB,MAAI,YAAY;AAChB,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,YAAY,aAAa,MAAM,OAAO,SAAS,GAAG;AACpD;AAAA,IACF,WAAW,aAAa,UAAU;AAChC,YAAM,WAAW,WAAW;AAC5B,UAAI,CAAC,UAAU;AACb,eAAO,MAAM,OAAO,CAAC,CAAC;AAAA,MACxB,OAAO;AACL,cAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC,IAAI;AACtD,cAAM,eAAe;AAAA,UACnB,OAAO,CAAC;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,mBAAa,gBAAgB,OAAO,CAAC,GAAG,OAAO,IAAI,CAAC,GAAG,OAAO;AAAA,IAChE;AAAA,EACF;AACA,SAAO,MAAM,OAAO,OAAO,SAAS,CAAC,CAAC;AACxC;AAGA,IAAO,qBAAQ;", "names": []}