{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Feature, FeatureCollection, Geometry } from \"geojson\";\nimport { earthRadius } from \"@turf/helpers\";\nimport { geomReduce } from \"@turf/meta\";\n\n/**\n * Calculates the geodesic area in square meters of one or more polygons.\n *\n * @function\n * @param {GeoJSON} geojson input polygon(s) as {@link Geometry}, {@link Feature}, or {@link FeatureCollection}\n * @returns {number} area in square meters\n * @example\n * var polygon = turf.polygon([[[125, -15], [113, -22], [154, -27], [144, -15], [125, -15]]]);\n *\n * var area = turf.area(polygon);\n *\n * //addToMap\n * var addToMap = [polygon]\n * polygon.properties.area = area\n */\nfunction area(geojson: Feature<any> | FeatureCollection<any> | Geometry) {\n  return geomReduce(\n    geojson,\n    (value, geom) => {\n      return value + calculateArea(geom);\n    },\n    0\n  );\n}\n\n/**\n * Calculate Area\n *\n * @private\n * @param {Geometry} geom GeoJSON Geometries\n * @returns {number} area\n */\nfunction calculateArea(geom: Geometry): number {\n  let total = 0;\n  let i;\n  switch (geom.type) {\n    case \"Polygon\":\n      return polygonArea(geom.coordinates);\n    case \"MultiPolygon\":\n      for (i = 0; i < geom.coordinates.length; i++) {\n        total += polygonArea(geom.coordinates[i]);\n      }\n      return total;\n    case \"Point\":\n    case \"MultiPoint\":\n    case \"LineString\":\n    case \"MultiLineString\":\n      return 0;\n  }\n  return 0;\n}\n\nfunction polygonArea(coords: any) {\n  let total = 0;\n  if (coords && coords.length > 0) {\n    total += Math.abs(ringArea(coords[0]));\n    for (let i = 1; i < coords.length; i++) {\n      total -= Math.abs(ringArea(coords[i]));\n    }\n  }\n  return total;\n}\n\n/**\n * @private\n * A constant factor used to compute the area of a polygon.\n * It's derived from the square of the Earth's radius divided by 2.\n *\n * @type {number}\n */\nconst FACTOR = (earthRadius * earthRadius) / 2;\n\n/**\n * @private\n * A constant used for converting degrees to radians.\n * Represents the ratio of PI to 180.\n *\n * @type {number}\n */\nconst PI_OVER_180 = Math.PI / 180;\n\n/**\n * @private\n * Calculate the approximate area of the polygon were it projected onto the earth.\n * Note that this area will be positive if ring is oriented clockwise, otherwise it will be negative.\n *\n * Reference:\n * Robert. G. Chamberlain and William H. Duquette, \"Some Algorithms for Polygons on a Sphere\",\n * JPL Publication 07-03, Jet Propulsion\n * Laboratory, Pasadena, CA, June 2007 https://trs.jpl.nasa.gov/handle/2014/40409\n *\n * @param {Array<Array<number>>} coords Ring Coordinates\n * @returns {number} The approximate signed geodesic area of the polygon in square meters.\n */\nfunction ringArea(coords: number[][]): number {\n  const coordsLength = coords.length - 1;\n\n  if (coordsLength <= 2) return 0;\n  let total = 0;\n\n  let i = 0;\n  while (i < coordsLength) {\n    const lower = coords[i];\n    const middle = coords[i + 1 === coordsLength ? 0 : i + 1];\n    const upper =\n      coords[i + 2 >= coordsLength ? (i + 2) % coordsLength : i + 2];\n\n    const lowerX = lower[0] * PI_OVER_180;\n    const middleY = middle[1] * PI_OVER_180;\n    const upperX = upper[0] * PI_OVER_180;\n\n    total += (upperX - lowerX) * Math.sin(middleY);\n\n    i++;\n  }\n\n  return total * FACTOR;\n}\n\nexport { area };\nexport default area;\n"], "mappings": ";AACA,SAAS,mBAAmB;AAC5B,SAAS,kBAAkB;AAiB3B,SAAS,KAAK,SAA2D;AACvE,SAAO;AAAA,IACL;AAAA,IACA,CAAC,OAAO,SAAS;AACf,aAAO,QAAQ,cAAc,IAAI;AAAA,IACnC;AAAA,IACA;AAAA,EACF;AACF;AASA,SAAS,cAAc,MAAwB;AAC7C,MAAI,QAAQ;AACZ,MAAI;AACJ,UAAQ,KAAK,MAAM;AAAA,IACjB,KAAK;AACH,aAAO,YAAY,KAAK,WAAW;AAAA,IACrC,KAAK;AACH,WAAK,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAC5C,iBAAS,YAAY,KAAK,YAAY,CAAC,CAAC;AAAA,MAC1C;AACA,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,EACX;AACA,SAAO;AACT;AAEA,SAAS,YAAY,QAAa;AAChC,MAAI,QAAQ;AACZ,MAAI,UAAU,OAAO,SAAS,GAAG;AAC/B,aAAS,KAAK,IAAI,SAAS,OAAO,CAAC,CAAC,CAAC;AACrC,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,eAAS,KAAK,IAAI,SAAS,OAAO,CAAC,CAAC,CAAC;AAAA,IACvC;AAAA,EACF;AACA,SAAO;AACT;AASA,IAAM,SAAU,cAAc,cAAe;AAS7C,IAAM,cAAc,KAAK,KAAK;AAe9B,SAAS,SAAS,QAA4B;AAC5C,QAAM,eAAe,OAAO,SAAS;AAErC,MAAI,gBAAgB,EAAG,QAAO;AAC9B,MAAI,QAAQ;AAEZ,MAAI,IAAI;AACR,SAAO,IAAI,cAAc;AACvB,UAAM,QAAQ,OAAO,CAAC;AACtB,UAAM,SAAS,OAAO,IAAI,MAAM,eAAe,IAAI,IAAI,CAAC;AACxD,UAAM,QACJ,OAAO,IAAI,KAAK,gBAAgB,IAAI,KAAK,eAAe,IAAI,CAAC;AAE/D,UAAM,SAAS,MAAM,CAAC,IAAI;AAC1B,UAAM,UAAU,OAAO,CAAC,IAAI;AAC5B,UAAM,SAAS,MAAM,CAAC,IAAI;AAE1B,cAAU,SAAS,UAAU,KAAK,IAAI,OAAO;AAE7C;AAAA,EACF;AAEA,SAAO,QAAQ;AACjB;AAGA,IAAO,oBAAQ;", "names": []}