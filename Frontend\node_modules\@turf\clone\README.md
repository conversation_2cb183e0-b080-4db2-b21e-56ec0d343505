# @turf/clone

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## clone

Returns a cloned copy of the passed GeoJSON Object, including possible 'Foreign Members'.
\~3-5x faster than the common JSON.parse + JSON.stringify combo method.

### Parameters

*   `geojson` **[GeoJSON][1]** GeoJSON Object

### Examples

```javascript
var line = turf.lineString([[-74, 40], [-78, 42], [-82, 35]], {color: 'red'});

var lineCloned = turf.clone(line);
```

Returns **[GeoJSON][1]** cloned G<PERSON><PERSON><PERSON>N Object

[1]: https://tools.ietf.org/html/rfc7946#section-3

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/clone
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
