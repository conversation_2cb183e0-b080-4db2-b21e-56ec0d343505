{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { FeatureCollection, Point, GeoJsonProperties } from \"geojson\";\nimport { clone } from \"@turf/clone\";\nimport { coordAll, featureEach } from \"@turf/meta\";\nimport skmeans from \"skmeans\";\n\ntype KmeansProps = GeoJsonProperties & {\n  cluster?: number;\n  centroid?: [number, number];\n};\n\n/**\n * Takes a set of {@link Point|points} and partition them into clusters using the k-mean .\n * It uses the [k-means algorithm](https://en.wikipedia.org/wiki/K-means_clustering)\n *\n * @function\n * @param {FeatureCollection<Point>} points to be clustered\n * @param {Object} [options={}] Optional parameters\n * @param {number} [options.numberOfClusters=Math.sqrt(numberOfPoints/2)] numberOfClusters that will be generated\n * @param {boolean} [options.mutate=false] allows GeoJSON input to be mutated (significant performance increase if true)\n * @returns {FeatureCollection<Point>} Clustered Points with an additional two properties associated to each Feature:\n * - {number} cluster - the associated clusterId\n * - {[number, number]} centroid - Centroid of the cluster [Longitude, Latitude]\n * @example\n * // create random points with random z-values in their properties\n * var points = turf.randomPoint(100, {bbox: [0, 30, 20, 50]});\n * var options = {numberOfClusters: 7};\n * var clustered = turf.clustersKmeans(points, options);\n *\n * //addToMap\n * var addToMap = [clustered];\n */\nfunction clustersKmeans(\n  points: FeatureCollection<Point>,\n  options: {\n    numberOfClusters?: number;\n    mutate?: boolean;\n  } = {}\n): FeatureCollection<Point, KmeansProps> {\n  // Default Params\n  var count = points.features.length;\n  options.numberOfClusters =\n    options.numberOfClusters || Math.round(Math.sqrt(count / 2));\n\n  // numberOfClusters can't be greater than the number of points\n  // fallbacks to count\n  if (options.numberOfClusters > count) options.numberOfClusters = count;\n\n  // Clone points to prevent any mutations (enabled by default)\n  if (options.mutate !== true) points = clone(points);\n\n  // collect points coordinates\n  var data = coordAll(points);\n\n  // create seed to avoid skmeans to drift\n  var initialCentroids = data.slice(0, options.numberOfClusters);\n\n  // create skmeans clusters\n  var skmeansResult = skmeans(data, options.numberOfClusters, initialCentroids);\n\n  // store centroids {clusterId: [number, number]}\n  var centroids: Record<string, number[]> = {};\n  (skmeansResult.centroids as number[][]).forEach(function (\n    coord: number[],\n    idx: number\n  ) {\n    centroids[idx] = coord;\n  });\n\n  // add associated cluster number\n  featureEach(points, function (point, index) {\n    var clusterId = skmeansResult.idxs[index];\n    point.properties!.cluster = clusterId;\n    point.properties!.centroid = centroids[clusterId];\n  });\n\n  return points as FeatureCollection<Point, KmeansProps>;\n}\n\nexport { clustersKmeans, KmeansProps };\nexport default clustersKmeans;\n"], "mappings": ";AACA,SAAS,aAAa;AACtB,SAAS,UAAU,mBAAmB;AACtC,OAAO,aAAa;AA4BpB,SAAS,eACP,QACA,UAGI,CAAC,GACkC;AAEvC,MAAI,QAAQ,OAAO,SAAS;AAC5B,UAAQ,mBACN,QAAQ,oBAAoB,KAAK,MAAM,KAAK,KAAK,QAAQ,CAAC,CAAC;AAI7D,MAAI,QAAQ,mBAAmB,MAAO,SAAQ,mBAAmB;AAGjE,MAAI,QAAQ,WAAW,KAAM,UAAS,MAAM,MAAM;AAGlD,MAAI,OAAO,SAAS,MAAM;AAG1B,MAAI,mBAAmB,KAAK,MAAM,GAAG,QAAQ,gBAAgB;AAG7D,MAAI,gBAAgB,QAAQ,MAAM,QAAQ,kBAAkB,gBAAgB;AAG5E,MAAI,YAAsC,CAAC;AAC3C,EAAC,cAAc,UAAyB,QAAQ,SAC9C,OACA,KACA;AACA,cAAU,GAAG,IAAI;AAAA,EACnB,CAAC;AAGD,cAAY,QAAQ,SAAU,OAAO,OAAO;AAC1C,QAAI,YAAY,cAAc,KAAK,KAAK;AACxC,UAAM,WAAY,UAAU;AAC5B,UAAM,WAAY,WAAW,UAAU,SAAS;AAAA,EAClD,CAAC;AAED,SAAO;AACT;AAGA,IAAO,+BAAQ;", "names": []}