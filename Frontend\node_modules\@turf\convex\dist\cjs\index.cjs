"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }// index.ts
var _helpers = require('@turf/helpers');
var _meta = require('@turf/meta');
var _concaveman = require('concaveman'); var _concaveman2 = _interopRequireDefault(_concaveman);
function convex(geojson, options = {}) {
  options.concavity = options.concavity || Infinity;
  const points = [];
  _meta.coordEach.call(void 0, geojson, (coord) => {
    points.push([coord[0], coord[1]]);
  });
  if (!points.length) {
    return null;
  }
  const convexHull = _concaveman2.default.call(void 0, points, options.concavity);
  if (convexHull.length > 3) {
    return _helpers.polygon.call(void 0, [convexHull]);
  }
  return null;
}
var turf_convex_default = convex;



exports.convex = convex; exports.default = turf_convex_default;
//# sourceMappingURL=index.cjs.map