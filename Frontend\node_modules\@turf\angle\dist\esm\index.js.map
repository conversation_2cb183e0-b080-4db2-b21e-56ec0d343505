{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { bearing } from \"@turf/bearing\";\nimport { bearingToAzimuth, Coord, isObject } from \"@turf/helpers\";\nimport { rhumbBearing } from \"@turf/rhumb-bearing\";\n\n/**\n * Finds the angle formed by two adjacent segments defined by 3 points. The result will be the (positive clockwise)\n * angle with origin on the `startPoint-midPoint` segment, or its explementary angle if required.\n *\n * @function\n * @param {Coord} startPoint Start Point Coordinates\n * @param {Coord} midPoint Mid Point Coordinates\n * @param {Coord} endPoint End Point Coordinates\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.explementary=false] Returns the explementary angle instead (360 - angle)\n * @param {boolean} [options.mercator=false] if calculations should be performed over Mercator or WGS84 projection\n * @returns {number} Angle between the provided points, or its explementary.\n * @example\n * turf.angle([5, 5], [5, 6], [3, 4]);\n * //=45\n */\nfunction angle(\n  startPoint: Coord,\n  midPoint: Coord,\n  endPoint: Coord,\n  options: {\n    explementary?: boolean;\n    mercator?: boolean;\n  } = {}\n): number {\n  // Optional Parameters\n  if (!isObject(options)) {\n    throw new Error(\"options is invalid\");\n  }\n\n  // Validation\n  if (!startPoint) {\n    throw new Error(\"startPoint is required\");\n  }\n  if (!midPoint) {\n    throw new Error(\"midPoint is required\");\n  }\n  if (!endPoint) {\n    throw new Error(\"endPoint is required\");\n  }\n\n  // Rename to shorter variables\n  const A = startPoint;\n  const O = midPoint;\n  const B = endPoint;\n\n  // Main\n  const azimuthOA = bearingToAzimuth(\n    options.mercator !== true ? bearing(O, A) : rhumbBearing(O, A)\n  );\n  let azimuthOB = bearingToAzimuth(\n    options.mercator !== true ? bearing(O, B) : rhumbBearing(O, B)\n  );\n  // If OB \"trails\" OA advance OB one revolution so we get the clockwise angle.\n  if (azimuthOB < azimuthOA) {\n    azimuthOB = azimuthOB + 360;\n  }\n  const angleAOB = azimuthOB - azimuthOA;\n\n  // Explementary angle\n  if (options.explementary === true) {\n    return 360 - angleAOB;\n  }\n  return angleAOB;\n}\n\nexport { angle };\nexport default angle;\n"], "mappings": ";AAAA,SAAS,eAAe;AACxB,SAAS,kBAAyB,gBAAgB;AAClD,SAAS,oBAAoB;AAkB7B,SAAS,MACP,YACA,UACA,UACA,UAGI,CAAC,GACG;AAER,MAAI,CAAC,SAAS,OAAO,GAAG;AACtB,UAAM,IAAI,MAAM,oBAAoB;AAAA,EACtC;AAGA,MAAI,CAAC,YAAY;AACf,UAAM,IAAI,MAAM,wBAAwB;AAAA,EAC1C;AACA,MAAI,CAAC,UAAU;AACb,UAAM,IAAI,MAAM,sBAAsB;AAAA,EACxC;AACA,MAAI,CAAC,UAAU;AACb,UAAM,IAAI,MAAM,sBAAsB;AAAA,EACxC;AAGA,QAAM,IAAI;AACV,QAAM,IAAI;AACV,QAAM,IAAI;AAGV,QAAM,YAAY;AAAA,IAChB,QAAQ,aAAa,OAAO,QAAQ,GAAG,CAAC,IAAI,aAAa,GAAG,CAAC;AAAA,EAC/D;AACA,MAAI,YAAY;AAAA,IACd,QAAQ,aAAa,OAAO,QAAQ,GAAG,CAAC,IAAI,aAAa,GAAG,CAAC;AAAA,EAC/D;AAEA,MAAI,YAAY,WAAW;AACzB,gBAAY,YAAY;AAAA,EAC1B;AACA,QAAM,WAAW,YAAY;AAG7B,MAAI,QAAQ,iBAAiB,MAAM;AACjC,WAAO,MAAM;AAAA,EACf;AACA,SAAO;AACT;AAGA,IAAO,qBAAQ;", "names": []}