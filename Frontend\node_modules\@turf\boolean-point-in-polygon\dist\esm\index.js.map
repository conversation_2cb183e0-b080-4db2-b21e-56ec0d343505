{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import pip from \"point-in-polygon-hao\";\nimport {\n  BBox,\n  Feature,\n  MultiPolygon,\n  Polygon,\n  GeoJsonProperties,\n} from \"geojson\";\nimport { <PERSON><PERSON> } from \"@turf/helpers\";\nimport { getCoord, getGeom } from \"@turf/invariant\";\n\n// http://en.wikipedia.org/wiki/Even%E2%80%93odd_rule\n// modified from: https://github.com/substack/point-in-polygon/blob/master/index.js\n// which was modified from http://www.ecse.rpi.edu/Homepages/wrf/Research/Short_Notes/pnpoly.html\n/**\n * Takes a {@link Point} and a {@link Polygon} or {@link MultiPolygon} and determines if the point\n * resides inside the polygon. The polygon can be convex or concave. The function accounts for holes.\n *\n * @function\n * @param {Coord} point input point\n * @param {Feature<Polygon|MultiPolygon>} polygon input polygon or multipolygon\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.ignoreBoundary=false] True if polygon boundary should be ignored when determining if\n * the point is inside the polygon otherwise false.\n * @returns {boolean} `true` if the Point is inside the Polygon; `false` if the Point is not inside the Polygon\n * @example\n * var pt = turf.point([-77, 44]);\n * var poly = turf.polygon([[\n *   [-81, 41],\n *   [-81, 47],\n *   [-72, 47],\n *   [-72, 41],\n *   [-81, 41]\n * ]]);\n *\n * turf.booleanPointInPolygon(pt, poly);\n * //= true\n */\nfunction booleanPointInPolygon<\n  G extends Polygon | MultiPolygon,\n  P extends GeoJsonProperties = GeoJsonProperties,\n>(\n  point: Coord,\n  polygon: Feature<G, P> | G,\n  options: {\n    ignoreBoundary?: boolean;\n  } = {}\n) {\n  // validation\n  if (!point) {\n    throw new Error(\"point is required\");\n  }\n  if (!polygon) {\n    throw new Error(\"polygon is required\");\n  }\n\n  const pt = getCoord(point);\n  const geom = getGeom(polygon);\n  const type = geom.type;\n  const bbox = polygon.bbox;\n  let polys: any[] = geom.coordinates;\n\n  // Quick elimination if point is not inside bbox\n  if (bbox && inBBox(pt, bbox) === false) {\n    return false;\n  }\n\n  if (type === \"Polygon\") {\n    polys = [polys];\n  }\n  let result = false;\n  for (var i = 0; i < polys.length; ++i) {\n    const polyResult = pip(pt, polys[i]);\n    if (polyResult === 0) return options.ignoreBoundary ? false : true;\n    else if (polyResult) result = true;\n  }\n\n  return result;\n}\n\n/**\n * inBBox\n *\n * @private\n * @param {Position} pt point [x,y]\n * @param {BBox} bbox BBox [west, south, east, north]\n * @returns {boolean} true/false if point is inside BBox\n */\nfunction inBBox(pt: number[], bbox: BBox) {\n  return (\n    bbox[0] <= pt[0] && bbox[1] <= pt[1] && bbox[2] >= pt[0] && bbox[3] >= pt[1]\n  );\n}\n\nexport { booleanPointInPolygon };\nexport default booleanPointInPolygon;\n"], "mappings": ";AAAA,OAAO,SAAS;AAShB,SAAS,UAAU,eAAe;AA6BlC,SAAS,sBAIP,OACA,SACA,UAEI,CAAC,GACL;AAEA,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,mBAAmB;AAAA,EACrC;AACA,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,qBAAqB;AAAA,EACvC;AAEA,QAAM,KAAK,SAAS,KAAK;AACzB,QAAM,OAAO,QAAQ,OAAO;AAC5B,QAAM,OAAO,KAAK;AAClB,QAAM,OAAO,QAAQ;AACrB,MAAI,QAAe,KAAK;AAGxB,MAAI,QAAQ,OAAO,IAAI,IAAI,MAAM,OAAO;AACtC,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,WAAW;AACtB,YAAQ,CAAC,KAAK;AAAA,EAChB;AACA,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,EAAE,GAAG;AACrC,UAAM,aAAa,IAAI,IAAI,MAAM,CAAC,CAAC;AACnC,QAAI,eAAe,EAAG,QAAO,QAAQ,iBAAiB,QAAQ;AAAA,aACrD,WAAY,UAAS;AAAA,EAChC;AAEA,SAAO;AACT;AAUA,SAAS,OAAO,IAAc,MAAY;AACxC,SACE,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC;AAE/E;AAGA,IAAO,wCAAQ;", "names": []}