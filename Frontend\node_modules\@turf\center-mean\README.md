# @turf/center-mean

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## centerMean

Takes a [Feature][1] or [FeatureCollection][2] and returns the mean center. Can be weighted.

### Parameters

*   `geojson` **[GeoJSON][3]** GeoJSON to be centered
*   `options` **[Object][4]** Optional parameters (optional, default `{}`)

    *   `options.properties` **[Object][4]** Translate GeoJSON Properties to Point (optional, default `{}`)
    *   `options.bbox` **[Object][4]** Translate GeoJSON BBox to Point (optional, default `{}`)
    *   `options.id` **[Object][4]** Translate GeoJSON Id to Point (optional, default `{}`)
    *   `options.weight` **[string][5]?** the property name used to weight the center

### Examples

```javascript
var features = turf.featureCollection([
  turf.point([-97.522259, 35.4691], {value: 10}),
  turf.point([-97.502754, 35.463455], {value: 3}),
  turf.point([-97.508269, 35.463245], {value: 5})
]);

var options = {weight: "value"}
var mean = turf.centerMean(features, options);

//addToMap
var addToMap = [features, mean]
mean.properties['marker-size'] = 'large';
mean.properties['marker-color'] = '#000';
```

Returns **[Feature][1]<[Point][6]>** a Point feature at the mean center point of all input features

[1]: https://tools.ietf.org/html/rfc7946#section-3.2

[2]: https://tools.ietf.org/html/rfc7946#section-3.3

[3]: https://tools.ietf.org/html/rfc7946#section-3

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[6]: https://tools.ietf.org/html/rfc7946#section-3.1.2

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/center-mean
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
