import { Position, Feature, Polygon, FeatureCollection, Point, GeoJsonProperties } from 'geojson';

declare interface SDEProps {
    meanCenterCoordinates: Position;
    semiMajorAxis: number;
    semiMinorAxis: number;
    numberOfFeatures: number;
    angle: number;
    percentageWithinEllipse: number;
}
declare interface StandardDeviationalEllipse extends Feature<Polygon> {
    properties: {
        standardDeviationalEllipse: SDEProps;
        [key: string]: any;
    } | null;
}
/**
 * Takes a collection of features and returns a standard deviational ellipse,
 * also known as a “directional distribution.” The standard deviational ellipse
 * aims to show the direction and the distribution of a dataset by drawing
 * an ellipse that contains about one standard deviation’s worth (~ 70%) of the
 * data.
 *
 * This module mirrors the functionality of {@link http://desktop.arcgis.com/en/arcmap/10.3/tools/spatial-statistics-toolbox/directional-distribution.htm|Directional Distribution}
 * in ArcGIS and the {@link http://arken.nmbu.no/~havatv/gis/qgisplugins/SDEllipse/|QGIS Standard Deviational Ellipse Plugin}
 *
 * **Bibliography**
 *
 * • <PERSON>, “The Standard Deviational Ellipse; An Updated Tool for
 * Spatial Description,” _Geografiska Annaler_ 53, no. 1 (1971): 28–39,
 * doi:{@link https://doi.org/10.2307/490885|10.2307/490885}.
 *
 * • Paul Hanly Furfey, “A Note on Lefever’s “Standard Deviational Ellipse,”
 * _American Journal of Sociology_ 33, no. 1 (1927): 94—98,
 * doi:{@link https://doi.org/10.1086/214336|10.1086/214336}.
 *
 *
 * @function
 * @param {FeatureCollection<Point>} points GeoJSON points
 * @param {Object} [options={}] Optional parameters
 * @param {string} [options.weight] the property name used to weight the center
 * @param {number} [options.steps=64] number of steps for the polygon
 * @param {Object} [options.properties={}] properties to pass to the resulting ellipse
 * @returns {Feature<Polygon>} an elliptical Polygon that includes approximately 1 SD of the dataset within it.
 * @example
 *
 * const bbox = [-74, 40.72, -73.98, 40.74];
 * const points = turf.randomPoint(400, {bbox: bbox});
 * const sdEllipse = turf.standardDeviationalEllipse(points);
 *
 * //addToMap
 * const addToMap = [points, sdEllipse];
 *
 */
declare function standardDeviationalEllipse(points: FeatureCollection<Point>, options?: {
    properties?: GeoJsonProperties;
    weight?: string;
    steps?: number;
}): StandardDeviationalEllipse;

export { type SDEProps, type StandardDeviationalEllipse, standardDeviationalEllipse as default, standardDeviationalEllipse };
