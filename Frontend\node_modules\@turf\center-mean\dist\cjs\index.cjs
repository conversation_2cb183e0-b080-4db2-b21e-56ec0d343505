"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _meta = require('@turf/meta');
var _helpers = require('@turf/helpers');
function centerMean(geojson, options = {}) {
  let sumXs = 0;
  let sumYs = 0;
  let sumNs = 0;
  _meta.geomEach.call(void 0, geojson, function(geom, featureIndex, properties) {
    let weight = options.weight ? properties == null ? void 0 : properties[options.weight] : void 0;
    weight = weight === void 0 || weight === null ? 1 : weight;
    if (!_helpers.isNumber.call(void 0, weight))
      throw new Error(
        "weight value must be a number for feature index " + featureIndex
      );
    weight = Number(weight);
    if (weight > 0) {
      _meta.coordEach.call(void 0, geom, function(coord) {
        sumXs += coord[0] * weight;
        sumYs += coord[1] * weight;
        sumNs += weight;
      });
    }
  });
  return _helpers.point.call(void 0, [sumXs / sumNs, sumYs / sumNs], options.properties, options);
}
var turf_center_mean_default = centerMean;



exports.centerMean = centerMean; exports.default = turf_center_mean_default;
//# sourceMappingURL=index.cjs.map