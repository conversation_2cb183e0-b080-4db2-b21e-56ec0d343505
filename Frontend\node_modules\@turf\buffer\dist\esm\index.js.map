{"version": 3, "sources": ["../../index.js"], "sourcesContent": ["import { center } from \"@turf/center\";\nimport jsts from \"@turf/jsts\";\nimport { geomEach, featureEach } from \"@turf/meta\";\nimport { geoAzimuthalEquidistant } from \"d3-geo\";\nimport {\n  feature,\n  featureCollection,\n  radiansToLength,\n  lengthToRadians,\n  earthRadius,\n} from \"@turf/helpers\";\n\nconst { <PERSON><PERSON><PERSON><PERSON><PERSON>, GeoJSONReader, GeoJSONWriter } = jsts;\n\n/**\n * Calculates a buffer for input features for a given radius. Units supported are miles, kilometers, and degrees.\n *\n * When using a negative radius, the resulting geometry may be invalid if\n * it's too small compared to the radius magnitude. If the input is a\n * FeatureCollection, only valid members will be returned in the output\n * FeatureCollection - i.e., the output collection may have fewer members than\n * the input, or even be empty.\n *\n * @function\n * @param {FeatureCollection|Geometry|Feature<any>} geojson input to be buffered\n * @param {number} radius distance to draw the buffer (negative values are allowed)\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.units=\"kilometers\"] any of the options supported by turf units\n * @param {number} [options.steps=8] number of steps\n * @returns {FeatureCollection|Feature<Polygon|MultiPolygon>|undefined} buffered features\n * @example\n * var point = turf.point([-90.548630, 14.616599]);\n * var buffered = turf.buffer(point, 500, {units: 'miles'});\n *\n * //addToMap\n * var addToMap = [point, buffered]\n */\nfunction buffer(geojson, radius, options) {\n  // Optional params\n  options = options || {};\n\n  // use user supplied options or default values\n  var units = options.units || \"kilometers\";\n  var steps = options.steps || 8;\n\n  // validation\n  if (!geojson) throw new Error(\"geojson is required\");\n  if (typeof options !== \"object\") throw new Error(\"options must be an object\");\n  if (typeof steps !== \"number\") throw new Error(\"steps must be an number\");\n\n  // Allow negative buffers (\"erosion\") or zero-sized buffers (\"repair geometry\")\n  if (radius === undefined) throw new Error(\"radius is required\");\n  if (steps <= 0) throw new Error(\"steps must be greater than 0\");\n\n  var results = [];\n  switch (geojson.type) {\n    case \"GeometryCollection\":\n      geomEach(geojson, function (geometry) {\n        var buffered = bufferFeature(geometry, radius, units, steps);\n        if (buffered) results.push(buffered);\n      });\n      return featureCollection(results);\n    case \"FeatureCollection\":\n      featureEach(geojson, function (feature) {\n        var multiBuffered = bufferFeature(feature, radius, units, steps);\n        if (multiBuffered) {\n          featureEach(multiBuffered, function (buffered) {\n            if (buffered) results.push(buffered);\n          });\n        }\n      });\n      return featureCollection(results);\n  }\n  return bufferFeature(geojson, radius, units, steps);\n}\n\n/**\n * Buffer single Feature/Geometry\n *\n * @private\n * @param {Feature<any>} geojson input to be buffered\n * @param {number} radius distance to draw the buffer\n * @param {string} [units='kilometers'] any of the options supported by turf units\n * @param {number} [steps=8] number of steps\n * @returns {Feature<Polygon|MultiPolygon>} buffered feature\n */\nfunction bufferFeature(geojson, radius, units, steps) {\n  var properties = geojson.properties || {};\n  var geometry = geojson.type === \"Feature\" ? geojson.geometry : geojson;\n\n  // Geometry Types faster than jsts\n  if (geometry.type === \"GeometryCollection\") {\n    var results = [];\n    geomEach(geojson, function (geometry) {\n      var buffered = bufferFeature(geometry, radius, units, steps);\n      if (buffered) results.push(buffered);\n    });\n    return featureCollection(results);\n  }\n\n  // Project GeoJSON to Azimuthal Equidistant projection (convert to Meters)\n  var projection = defineProjection(geometry);\n  var projected = {\n    type: geometry.type,\n    coordinates: projectCoords(geometry.coordinates, projection),\n  };\n\n  // JSTS buffer operation\n  var reader = new GeoJSONReader();\n  var geom = reader.read(projected);\n  var distance = radiansToLength(lengthToRadians(radius, units), \"meters\");\n  var buffered = BufferOp.bufferOp(geom, distance, steps);\n  var writer = new GeoJSONWriter();\n  buffered = writer.write(buffered);\n\n  // Detect if empty geometries\n  if (coordsIsNaN(buffered.coordinates)) return undefined;\n\n  // Unproject coordinates (convert to Degrees)\n  var result = {\n    type: buffered.type,\n    coordinates: unprojectCoords(buffered.coordinates, projection),\n  };\n\n  return feature(result, properties);\n}\n\n/**\n * Coordinates isNaN\n *\n * @private\n * @param {Array<any>} coords GeoJSON Coordinates\n * @returns {boolean} if NaN exists\n */\nfunction coordsIsNaN(coords) {\n  if (Array.isArray(coords[0])) return coordsIsNaN(coords[0]);\n  return isNaN(coords[0]);\n}\n\n/**\n * Project coordinates to projection\n *\n * @private\n * @param {Array<any>} coords to project\n * @param {GeoProjection} proj D3 Geo Projection\n * @returns {Array<any>} projected coordinates\n */\nfunction projectCoords(coords, proj) {\n  if (typeof coords[0] !== \"object\") return proj(coords);\n  return coords.map(function (coord) {\n    return projectCoords(coord, proj);\n  });\n}\n\n/**\n * Un-Project coordinates to projection\n *\n * @private\n * @param {Array<any>} coords to un-project\n * @param {GeoProjection} proj D3 Geo Projection\n * @returns {Array<any>} un-projected coordinates\n */\nfunction unprojectCoords(coords, proj) {\n  if (typeof coords[0] !== \"object\") return proj.invert(coords);\n  return coords.map(function (coord) {\n    return unprojectCoords(coord, proj);\n  });\n}\n\n/**\n * Define Azimuthal Equidistant projection\n *\n * @private\n * @param {Geometry|Feature<any>} geojson Base projection on center of GeoJSON\n * @returns {GeoProjection} D3 Geo Azimuthal Equidistant Projection\n */\nfunction defineProjection(geojson) {\n  var coords = center(geojson).geometry.coordinates;\n  var rotation = [-coords[0], -coords[1]];\n  return geoAzimuthalEquidistant().rotate(rotation).scale(earthRadius);\n}\n\nexport { buffer };\nexport default buffer;\n"], "mappings": ";AAAA,SAAS,cAAc;AACvB,OAAO,UAAU;AACjB,SAAS,UAAU,mBAAmB;AACtC,SAAS,+BAA+B;AACxC;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAEP,IAAM,EAAE,UAAU,eAAe,cAAc,IAAI;AAyBnD,SAAS,OAAO,SAAS,QAAQ,SAAS;AAExC,YAAU,WAAW,CAAC;AAGtB,MAAI,QAAQ,QAAQ,SAAS;AAC7B,MAAI,QAAQ,QAAQ,SAAS;AAG7B,MAAI,CAAC,QAAS,OAAM,IAAI,MAAM,qBAAqB;AACnD,MAAI,OAAO,YAAY,SAAU,OAAM,IAAI,MAAM,2BAA2B;AAC5E,MAAI,OAAO,UAAU,SAAU,OAAM,IAAI,MAAM,yBAAyB;AAGxE,MAAI,WAAW,OAAW,OAAM,IAAI,MAAM,oBAAoB;AAC9D,MAAI,SAAS,EAAG,OAAM,IAAI,MAAM,8BAA8B;AAE9D,MAAI,UAAU,CAAC;AACf,UAAQ,QAAQ,MAAM;AAAA,IACpB,KAAK;AACH,eAAS,SAAS,SAAU,UAAU;AACpC,YAAI,WAAW,cAAc,UAAU,QAAQ,OAAO,KAAK;AAC3D,YAAI,SAAU,SAAQ,KAAK,QAAQ;AAAA,MACrC,CAAC;AACD,aAAO,kBAAkB,OAAO;AAAA,IAClC,KAAK;AACH,kBAAY,SAAS,SAAUA,UAAS;AACtC,YAAI,gBAAgB,cAAcA,UAAS,QAAQ,OAAO,KAAK;AAC/D,YAAI,eAAe;AACjB,sBAAY,eAAe,SAAU,UAAU;AAC7C,gBAAI,SAAU,SAAQ,KAAK,QAAQ;AAAA,UACrC,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,aAAO,kBAAkB,OAAO;AAAA,EACpC;AACA,SAAO,cAAc,SAAS,QAAQ,OAAO,KAAK;AACpD;AAYA,SAAS,cAAc,SAAS,QAAQ,OAAO,OAAO;AACpD,MAAI,aAAa,QAAQ,cAAc,CAAC;AACxC,MAAI,WAAW,QAAQ,SAAS,YAAY,QAAQ,WAAW;AAG/D,MAAI,SAAS,SAAS,sBAAsB;AAC1C,QAAI,UAAU,CAAC;AACf,aAAS,SAAS,SAAUC,WAAU;AACpC,UAAIC,YAAW,cAAcD,WAAU,QAAQ,OAAO,KAAK;AAC3D,UAAIC,UAAU,SAAQ,KAAKA,SAAQ;AAAA,IACrC,CAAC;AACD,WAAO,kBAAkB,OAAO;AAAA,EAClC;AAGA,MAAI,aAAa,iBAAiB,QAAQ;AAC1C,MAAI,YAAY;AAAA,IACd,MAAM,SAAS;AAAA,IACf,aAAa,cAAc,SAAS,aAAa,UAAU;AAAA,EAC7D;AAGA,MAAI,SAAS,IAAI,cAAc;AAC/B,MAAI,OAAO,OAAO,KAAK,SAAS;AAChC,MAAI,WAAW,gBAAgB,gBAAgB,QAAQ,KAAK,GAAG,QAAQ;AACvE,MAAI,WAAW,SAAS,SAAS,MAAM,UAAU,KAAK;AACtD,MAAI,SAAS,IAAI,cAAc;AAC/B,aAAW,OAAO,MAAM,QAAQ;AAGhC,MAAI,YAAY,SAAS,WAAW,EAAG,QAAO;AAG9C,MAAI,SAAS;AAAA,IACX,MAAM,SAAS;AAAA,IACf,aAAa,gBAAgB,SAAS,aAAa,UAAU;AAAA,EAC/D;AAEA,SAAO,QAAQ,QAAQ,UAAU;AACnC;AASA,SAAS,YAAY,QAAQ;AAC3B,MAAI,MAAM,QAAQ,OAAO,CAAC,CAAC,EAAG,QAAO,YAAY,OAAO,CAAC,CAAC;AAC1D,SAAO,MAAM,OAAO,CAAC,CAAC;AACxB;AAUA,SAAS,cAAc,QAAQ,MAAM;AACnC,MAAI,OAAO,OAAO,CAAC,MAAM,SAAU,QAAO,KAAK,MAAM;AACrD,SAAO,OAAO,IAAI,SAAU,OAAO;AACjC,WAAO,cAAc,OAAO,IAAI;AAAA,EAClC,CAAC;AACH;AAUA,SAAS,gBAAgB,QAAQ,MAAM;AACrC,MAAI,OAAO,OAAO,CAAC,MAAM,SAAU,QAAO,KAAK,OAAO,MAAM;AAC5D,SAAO,OAAO,IAAI,SAAU,OAAO;AACjC,WAAO,gBAAgB,OAAO,IAAI;AAAA,EACpC,CAAC;AACH;AASA,SAAS,iBAAiB,SAAS;AACjC,MAAI,SAAS,OAAO,OAAO,EAAE,SAAS;AACtC,MAAI,WAAW,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AACtC,SAAO,wBAAwB,EAAE,OAAO,QAAQ,EAAE,MAAM,WAAW;AACrE;AAGA,IAAO,sBAAQ;", "names": ["feature", "geometry", "buffered"]}