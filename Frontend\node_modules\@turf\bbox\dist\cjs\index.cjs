"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _meta = require('@turf/meta');
function bbox(geojson, options = {}) {
  if (geojson.bbox != null && true !== options.recompute) {
    return geojson.bbox;
  }
  const result = [Infinity, Infinity, -Infinity, -Infinity];
  _meta.coordEach.call(void 0, geojson, (coord) => {
    if (result[0] > coord[0]) {
      result[0] = coord[0];
    }
    if (result[1] > coord[1]) {
      result[1] = coord[1];
    }
    if (result[2] < coord[0]) {
      result[2] = coord[0];
    }
    if (result[3] < coord[1]) {
      result[3] = coord[1];
    }
  });
  return result;
}
var turf_bbox_default = bbox;



exports.bbox = bbox; exports.default = turf_bbox_default;
//# sourceMappingURL=index.cjs.map