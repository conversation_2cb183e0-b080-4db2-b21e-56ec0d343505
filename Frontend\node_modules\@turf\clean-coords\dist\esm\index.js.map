{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Position } from \"geojson\";\nimport { feature } from \"@turf/helpers\";\nimport { getCoords, getType } from \"@turf/invariant\";\n\n// To-Do => Improve Typescript GeoJSON handling\n\n/**\n * Removes redundant coordinates from any GeoJSON Geometry.\n *\n * @function\n * @param {Geometry|Feature} geojson Feature or Geometry\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.mutate=false] allows GeoJSON input to be mutated\n * @returns {Geometry|Feature} the cleaned input Feature/Geometry\n * @example\n * var line = turf.lineString([[0, 0], [0, 2], [0, 5], [0, 8], [0, 8], [0, 10]]);\n * var multiPoint = turf.multiPoint([[0, 0], [0, 0], [2, 2]]);\n *\n * turf.cleanCoords(line).geometry.coordinates;\n * //= [[0, 0], [0, 10]]\n *\n * turf.cleanCoords(multiPoint).geometry.coordinates;\n * //= [[0, 0], [2, 2]]\n */\nfunction cleanCoords(\n  geojson: any,\n  options: {\n    mutate?: boolean;\n  } = {}\n) {\n  // Backwards compatible with v4.0\n  var mutate = typeof options === \"object\" ? options.mutate : options;\n  if (!geojson) throw new Error(\"geojson is required\");\n  var type = getType(geojson);\n\n  // Store new \"clean\" points in this Array\n  var newCoords = [];\n\n  switch (type) {\n    case \"LineString\":\n      newCoords = cleanLine(geojson, type);\n      break;\n    case \"MultiLineString\":\n    case \"Polygon\":\n      getCoords(geojson).forEach(function (line) {\n        newCoords.push(cleanLine(line, type));\n      });\n      break;\n    case \"MultiPolygon\":\n      getCoords(geojson).forEach(function (polygons: any) {\n        var polyPoints: Position[] = [];\n        polygons.forEach(function (ring: Position[]) {\n          polyPoints.push(cleanLine(ring, type));\n        });\n        newCoords.push(polyPoints);\n      });\n      break;\n    case \"Point\":\n      return geojson;\n    case \"MultiPoint\":\n      var existing: Record<string, true> = {};\n      getCoords(geojson).forEach(function (coord: any) {\n        var key = coord.join(\"-\");\n        if (!Object.prototype.hasOwnProperty.call(existing, key)) {\n          newCoords.push(coord);\n          existing[key] = true;\n        }\n      });\n      break;\n    default:\n      throw new Error(type + \" geometry not supported\");\n  }\n\n  // Support input mutation\n  if (geojson.coordinates) {\n    if (mutate === true) {\n      geojson.coordinates = newCoords;\n      return geojson;\n    }\n    return { type: type, coordinates: newCoords };\n  } else {\n    if (mutate === true) {\n      geojson.geometry.coordinates = newCoords;\n      return geojson;\n    }\n    return feature({ type: type, coordinates: newCoords }, geojson.properties, {\n      bbox: geojson.bbox,\n      id: geojson.id,\n    });\n  }\n}\n\n/**\n * Clean Coords\n *\n * @private\n * @param {Array<number>|LineString} line Line\n * @param {string} type Type of geometry\n * @returns {Array<number>} Cleaned coordinates\n */\nfunction cleanLine(line: Position[], type: string) {\n  var points = getCoords(line);\n  // handle \"clean\" segment\n  if (points.length === 2 && !equals(points[0], points[1])) return points;\n\n  var newPoints = [];\n  var secondToLast = points.length - 1;\n  var newPointsLength = newPoints.length;\n\n  newPoints.push(points[0]);\n  for (var i = 1; i < secondToLast; i++) {\n    var prevAddedPoint = newPoints[newPoints.length - 1];\n    if (\n      points[i][0] === prevAddedPoint[0] &&\n      points[i][1] === prevAddedPoint[1]\n    )\n      continue;\n    else {\n      newPoints.push(points[i]);\n      newPointsLength = newPoints.length;\n      if (newPointsLength > 2) {\n        if (\n          isPointOnLineSegment(\n            newPoints[newPointsLength - 3],\n            newPoints[newPointsLength - 1],\n            newPoints[newPointsLength - 2]\n          )\n        )\n          newPoints.splice(newPoints.length - 2, 1);\n      }\n    }\n  }\n  newPoints.push(points[points.length - 1]);\n  newPointsLength = newPoints.length;\n\n  // (Multi)Polygons must have at least 4 points, but a closed LineString with only 3 points is acceptable\n  if (\n    (type === \"Polygon\" || type === \"MultiPolygon\") &&\n    equals(points[0], points[points.length - 1]) &&\n    newPointsLength < 4\n  ) {\n    throw new Error(\"invalid polygon\");\n  }\n\n  if (type === \"LineString\" && newPointsLength < 3) {\n    return newPoints;\n  }\n\n  if (\n    isPointOnLineSegment(\n      newPoints[newPointsLength - 3],\n      newPoints[newPointsLength - 1],\n      newPoints[newPointsLength - 2]\n    )\n  )\n    newPoints.splice(newPoints.length - 2, 1);\n\n  return newPoints;\n}\n\n/**\n * Compares two points and returns if they are equals\n *\n * @private\n * @param {Position} pt1 point\n * @param {Position} pt2 point\n * @returns {boolean} true if they are equals\n */\nfunction equals(pt1: Position, pt2: Position) {\n  return pt1[0] === pt2[0] && pt1[1] === pt2[1];\n}\n\n/**\n * Returns if `point` is on the segment between `start` and `end`.\n * Borrowed from `@turf/boolean-point-on-line` to speed up the evaluation (instead of using the module as dependency)\n *\n * @private\n * @param {Position} start coord pair of start of line\n * @param {Position} end coord pair of end of line\n * @param {Position} point coord pair of point to check\n * @returns {boolean} true/false\n */\nfunction isPointOnLineSegment(start: Position, end: Position, point: Position) {\n  var x = point[0],\n    y = point[1];\n  var startX = start[0],\n    startY = start[1];\n  var endX = end[0],\n    endY = end[1];\n\n  var dxc = x - startX;\n  var dyc = y - startY;\n  var dxl = endX - startX;\n  var dyl = endY - startY;\n  var cross = dxc * dyl - dyc * dxl;\n\n  if (cross !== 0) return false;\n  else if (Math.abs(dxl) >= Math.abs(dyl))\n    return dxl > 0 ? startX <= x && x <= endX : endX <= x && x <= startX;\n  else return dyl > 0 ? startY <= y && y <= endY : endY <= y && y <= startY;\n}\n\nexport { cleanCoords };\nexport default cleanCoords;\n"], "mappings": ";AACA,SAAS,eAAe;AACxB,SAAS,WAAW,eAAe;AAsBnC,SAAS,YACP,SACA,UAEI,CAAC,GACL;AAEA,MAAI,SAAS,OAAO,YAAY,WAAW,QAAQ,SAAS;AAC5D,MAAI,CAAC,QAAS,OAAM,IAAI,MAAM,qBAAqB;AACnD,MAAI,OAAO,QAAQ,OAAO;AAG1B,MAAI,YAAY,CAAC;AAEjB,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,kBAAY,UAAU,SAAS,IAAI;AACnC;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AACH,gBAAU,OAAO,EAAE,QAAQ,SAAU,MAAM;AACzC,kBAAU,KAAK,UAAU,MAAM,IAAI,CAAC;AAAA,MACtC,CAAC;AACD;AAAA,IACF,KAAK;AACH,gBAAU,OAAO,EAAE,QAAQ,SAAU,UAAe;AAClD,YAAI,aAAyB,CAAC;AAC9B,iBAAS,QAAQ,SAAU,MAAkB;AAC3C,qBAAW,KAAK,UAAU,MAAM,IAAI,CAAC;AAAA,QACvC,CAAC;AACD,kBAAU,KAAK,UAAU;AAAA,MAC3B,CAAC;AACD;AAAA,IACF,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,UAAI,WAAiC,CAAC;AACtC,gBAAU,OAAO,EAAE,QAAQ,SAAU,OAAY;AAC/C,YAAI,MAAM,MAAM,KAAK,GAAG;AACxB,YAAI,CAAC,OAAO,UAAU,eAAe,KAAK,UAAU,GAAG,GAAG;AACxD,oBAAU,KAAK,KAAK;AACpB,mBAAS,GAAG,IAAI;AAAA,QAClB;AAAA,MACF,CAAC;AACD;AAAA,IACF;AACE,YAAM,IAAI,MAAM,OAAO,yBAAyB;AAAA,EACpD;AAGA,MAAI,QAAQ,aAAa;AACvB,QAAI,WAAW,MAAM;AACnB,cAAQ,cAAc;AACtB,aAAO;AAAA,IACT;AACA,WAAO,EAAE,MAAY,aAAa,UAAU;AAAA,EAC9C,OAAO;AACL,QAAI,WAAW,MAAM;AACnB,cAAQ,SAAS,cAAc;AAC/B,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,EAAE,MAAY,aAAa,UAAU,GAAG,QAAQ,YAAY;AAAA,MACzE,MAAM,QAAQ;AAAA,MACd,IAAI,QAAQ;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAUA,SAAS,UAAU,MAAkB,MAAc;AACjD,MAAI,SAAS,UAAU,IAAI;AAE3B,MAAI,OAAO,WAAW,KAAK,CAAC,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC,EAAG,QAAO;AAEjE,MAAI,YAAY,CAAC;AACjB,MAAI,eAAe,OAAO,SAAS;AACnC,MAAI,kBAAkB,UAAU;AAEhC,YAAU,KAAK,OAAO,CAAC,CAAC;AACxB,WAAS,IAAI,GAAG,IAAI,cAAc,KAAK;AACrC,QAAI,iBAAiB,UAAU,UAAU,SAAS,CAAC;AACnD,QACE,OAAO,CAAC,EAAE,CAAC,MAAM,eAAe,CAAC,KACjC,OAAO,CAAC,EAAE,CAAC,MAAM,eAAe,CAAC;AAEjC;AAAA,SACG;AACH,gBAAU,KAAK,OAAO,CAAC,CAAC;AACxB,wBAAkB,UAAU;AAC5B,UAAI,kBAAkB,GAAG;AACvB,YACE;AAAA,UACE,UAAU,kBAAkB,CAAC;AAAA,UAC7B,UAAU,kBAAkB,CAAC;AAAA,UAC7B,UAAU,kBAAkB,CAAC;AAAA,QAC/B;AAEA,oBAAU,OAAO,UAAU,SAAS,GAAG,CAAC;AAAA,MAC5C;AAAA,IACF;AAAA,EACF;AACA,YAAU,KAAK,OAAO,OAAO,SAAS,CAAC,CAAC;AACxC,oBAAkB,UAAU;AAG5B,OACG,SAAS,aAAa,SAAS,mBAChC,OAAO,OAAO,CAAC,GAAG,OAAO,OAAO,SAAS,CAAC,CAAC,KAC3C,kBAAkB,GAClB;AACA,UAAM,IAAI,MAAM,iBAAiB;AAAA,EACnC;AAEA,MAAI,SAAS,gBAAgB,kBAAkB,GAAG;AAChD,WAAO;AAAA,EACT;AAEA,MACE;AAAA,IACE,UAAU,kBAAkB,CAAC;AAAA,IAC7B,UAAU,kBAAkB,CAAC;AAAA,IAC7B,UAAU,kBAAkB,CAAC;AAAA,EAC/B;AAEA,cAAU,OAAO,UAAU,SAAS,GAAG,CAAC;AAE1C,SAAO;AACT;AAUA,SAAS,OAAO,KAAe,KAAe;AAC5C,SAAO,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC;AAC9C;AAYA,SAAS,qBAAqB,OAAiB,KAAe,OAAiB;AAC7E,MAAI,IAAI,MAAM,CAAC,GACb,IAAI,MAAM,CAAC;AACb,MAAI,SAAS,MAAM,CAAC,GAClB,SAAS,MAAM,CAAC;AAClB,MAAI,OAAO,IAAI,CAAC,GACd,OAAO,IAAI,CAAC;AAEd,MAAI,MAAM,IAAI;AACd,MAAI,MAAM,IAAI;AACd,MAAI,MAAM,OAAO;AACjB,MAAI,MAAM,OAAO;AACjB,MAAI,QAAQ,MAAM,MAAM,MAAM;AAE9B,MAAI,UAAU,EAAG,QAAO;AAAA,WACf,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG;AACpC,WAAO,MAAM,IAAI,UAAU,KAAK,KAAK,OAAO,QAAQ,KAAK,KAAK;AAAA,MAC3D,QAAO,MAAM,IAAI,UAAU,KAAK,KAAK,OAAO,QAAQ,KAAK,KAAK;AACrE;AAGA,IAAO,4BAAQ;", "names": []}