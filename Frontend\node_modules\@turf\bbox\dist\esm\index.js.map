{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { BB<PERSON> } from \"geojson\";\nimport { AllGeoJSON } from \"@turf/helpers\";\nimport { coordEach } from \"@turf/meta\";\n\n/**\n * Calculates the bounding box for any GeoJSON object, including FeatureCollection.\n * Uses geojson.bbox if available and options.recompute is not set.\n *\n * @function\n * @param {GeoJSON} geojson any GeoJSON object\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.recompute] Whether to ignore an existing bbox property on geojson\n * @returns {BBox} bbox extent in [minX, minY, maxX, maxY] order\n * @example\n * var line = turf.lineString([[-74, 40], [-78, 42], [-82, 35]]);\n * var bbox = turf.bbox(line);\n * var bboxPolygon = turf.bboxPolygon(bbox);\n *\n * //addToMap\n * var addToMap = [line, bboxPolygon]\n */\nfunction bbox(\n  geojson: AllGeoJSON,\n  options: {\n    recompute?: boolean;\n  } = {}\n): BBox {\n  if (geojson.bbox != null && true !== options.recompute) {\n    return geojson.bbox;\n  }\n  const result: BBox = [Infinity, Infinity, -Infinity, -Infinity];\n  coordEach(geojson, (coord) => {\n    if (result[0] > coord[0]) {\n      result[0] = coord[0];\n    }\n    if (result[1] > coord[1]) {\n      result[1] = coord[1];\n    }\n    if (result[2] < coord[0]) {\n      result[2] = coord[0];\n    }\n    if (result[3] < coord[1]) {\n      result[3] = coord[1];\n    }\n  });\n  return result;\n}\n\nexport { bbox };\nexport default bbox;\n"], "mappings": ";AAEA,SAAS,iBAAiB;AAmB1B,SAAS,KACP,SACA,UAEI,CAAC,GACC;AACN,MAAI,QAAQ,QAAQ,QAAQ,SAAS,QAAQ,WAAW;AACtD,WAAO,QAAQ;AAAA,EACjB;AACA,QAAM,SAAe,CAAC,UAAU,UAAU,WAAW,SAAS;AAC9D,YAAU,SAAS,CAAC,UAAU;AAC5B,QAAI,OAAO,CAAC,IAAI,MAAM,CAAC,GAAG;AACxB,aAAO,CAAC,IAAI,MAAM,CAAC;AAAA,IACrB;AACA,QAAI,OAAO,CAAC,IAAI,MAAM,CAAC,GAAG;AACxB,aAAO,CAAC,IAAI,MAAM,CAAC;AAAA,IACrB;AACA,QAAI,OAAO,CAAC,IAAI,MAAM,CAAC,GAAG;AACxB,aAAO,CAAC,IAAI,MAAM,CAAC;AAAA,IACrB;AACA,QAAI,OAAO,CAAC,IAAI,MAAM,CAAC,GAAG;AACxB,aAAO,CAAC,IAAI,MAAM,CAAC;AAAA,IACrB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAGA,IAAO,oBAAQ;", "names": []}