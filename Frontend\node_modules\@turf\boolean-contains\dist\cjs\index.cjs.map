{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-boolean-contains/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACUA,kCAAiC;AACjC,uEAAsC;AACtC,iEAAoD;AACpD,4CAAwB;AAmBxB,SAAS,eAAA,CACP,QAAA,EACA,QAAA,EACA;AACA,EAAA,MAAM,MAAA,EAAQ,gCAAA,QAAgB,CAAA;AAC9B,EAAA,MAAM,MAAA,EAAQ,gCAAA,QAAgB,CAAA;AAC9B,EAAA,MAAM,MAAA,EAAQ,KAAA,CAAM,IAAA;AACpB,EAAA,MAAM,MAAA,EAAQ,KAAA,CAAM,IAAA;AACpB,EAAA,MAAM,QAAA,EAAU,KAAA,CAAM,WAAA;AACtB,EAAA,MAAM,QAAA,EAAU,KAAA,CAAM,WAAA;AAEtB,EAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,IACb,KAAK,OAAA;AACH,MAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,QACb,KAAK,OAAA;AACH,UAAA,OAAO,aAAA,CAAc,OAAA,EAAS,OAAO,CAAA;AAAA,QACvC,OAAA;AACE,UAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,MACnE;AAAA,IACF,KAAK,YAAA;AACH,MAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,QACb,KAAK,OAAA;AACH,UAAA,OAAO,mBAAA,CAAoB,KAAA,EAAO,KAAK,CAAA;AAAA,QACzC,KAAK,YAAA;AACH,UAAA,OAAO,wBAAA,CAAyB,KAAA,EAAO,KAAK,CAAA;AAAA,QAC9C,OAAA;AACE,UAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,MACnE;AAAA,IACF,KAAK,YAAA;AACH,MAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,QACb,KAAK,OAAA;AACH,UAAA,OAAO,oDAAA,KAAc,EAAO,KAAA,EAAO,EAAE,iBAAA,EAAmB,KAAK,CAAC,CAAA;AAAA,QAChE,KAAK,YAAA;AACH,UAAA,OAAO,YAAA,CAAa,KAAA,EAAO,KAAK,CAAA;AAAA,QAClC,KAAK,YAAA;AACH,UAAA,OAAO,kBAAA,CAAmB,KAAA,EAAO,KAAK,CAAA;AAAA,QACxC,OAAA;AACE,UAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,MACnE;AAAA,IACF,KAAK,SAAA;AACH,MAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,QACb,KAAK,OAAA;AACH,UAAA,OAAO,0DAAA,KAAsB,EAAO,KAAA,EAAO,EAAE,cAAA,EAAgB,KAAK,CAAC,CAAA;AAAA,QACrE,KAAK,YAAA;AACH,UAAA,OAAO,YAAA,CAAa,KAAA,EAAO,KAAK,CAAA;AAAA,QAClC,KAAK,SAAA;AACH,UAAA,OAAO,YAAA,CAAa,KAAA,EAAO,KAAK,CAAA;AAAA,QAClC,KAAK,YAAA;AACH,UAAA,OAAO,kBAAA,CAAmB,KAAA,EAAO,KAAK,CAAA;AAAA,QACxC,OAAA;AACE,UAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,MACnE;AAAA,IACF,KAAK,cAAA;AACH,MAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,QACb,KAAK,SAAA;AACH,UAAA,OAAO,uBAAA,CAAwB,KAAA,EAAO,KAAK,CAAA;AAAA,QAC7C,OAAA;AACE,UAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,MACnE;AAAA,IACF,OAAA;AACE,MAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,EACnE;AACF;AAEA,SAAS,uBAAA,CAAwB,YAAA,EAA4B,OAAA,EAAkB;AAC7E,EAAA,OAAO,YAAA,CAAa,WAAA,CAAY,IAAA;AAAA,IAAK,CAAC,MAAA,EAAA,GACpC,YAAA,CAAa,EAAE,IAAA,EAAM,SAAA,EAAW,WAAA,EAAa,OAAO,CAAA,EAAG,OAAO;AAAA,EAChE,CAAA;AACF;AAEA,SAAS,mBAAA,CAAoB,UAAA,EAAwB,EAAA,EAAW;AAC9D,EAAA,IAAI,CAAA;AACJ,EAAA,IAAI,OAAA,EAAS,KAAA;AACb,EAAA,IAAA,CAAK,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,UAAA,CAAW,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AAClD,IAAA,GAAA,CAAI,aAAA,CAAc,UAAA,CAAW,WAAA,CAAY,CAAC,CAAA,EAAG,EAAA,CAAG,WAAW,CAAA,EAAG;AAC5D,MAAA,OAAA,EAAS,IAAA;AACT,MAAA,KAAA;AAAA,IACF;AAAA,EACF;AACA,EAAA,OAAO,MAAA;AACT;AAEA,SAAS,wBAAA,CACP,WAAA,EACA,WAAA,EACA;AACA,EAAA,IAAA,CAAA,MAAW,OAAA,GAAU,WAAA,CAAY,WAAA,EAAa;AAC5C,IAAA,IAAI,WAAA,EAAa,KAAA;AACjB,IAAA,IAAA,CAAA,MAAW,OAAA,GAAU,WAAA,CAAY,WAAA,EAAa;AAC5C,MAAA,GAAA,CAAI,aAAA,CAAc,MAAA,EAAQ,MAAM,CAAA,EAAG;AACjC,QAAA,WAAA,EAAa,IAAA;AACb,QAAA,KAAA;AAAA,MACF;AAAA,IACF;AACA,IAAA,GAAA,CAAI,CAAC,UAAA,EAAY;AACf,MAAA,OAAO,KAAA;AAAA,IACT;AAAA,EACF;AACA,EAAA,OAAO,IAAA;AACT;AAEA,SAAS,kBAAA,CAAmB,UAAA,EAAwB,UAAA,EAAwB;AAC1E,EAAA,IAAI,uBAAA,EAAyB,KAAA;AAC7B,EAAA,IAAA,CAAA,MAAW,MAAA,GAAS,UAAA,CAAW,WAAA,EAAa;AAC1C,IAAA,GAAA,CAAI,oDAAA,KAAc,EAAO,UAAA,EAAY,EAAE,iBAAA,EAAmB,KAAK,CAAC,CAAA,EAAG;AACjE,MAAA,uBAAA,EAAyB,IAAA;AAAA,IAC3B;AACA,IAAA,GAAA,CAAI,CAAC,oDAAA,KAAc,EAAO,UAAU,CAAA,EAAG;AACrC,MAAA,OAAO,KAAA;AAAA,IACT;AAAA,EACF;AACA,EAAA,GAAA,CAAI,sBAAA,EAAwB;AAC1B,IAAA,OAAO,IAAA;AAAA,EACT;AACA,EAAA,OAAO,KAAA;AACT;AAEA,SAAS,kBAAA,CAAmB,OAAA,EAAkB,UAAA,EAAwB;AACpE,EAAA,IAAA,CAAA,MAAW,MAAA,GAAS,UAAA,CAAW,WAAA,EAAa;AAC1C,IAAA,GAAA,CAAI,CAAC,0DAAA,KAAsB,EAAO,OAAA,EAAS,EAAE,cAAA,EAAgB,KAAK,CAAC,CAAA,EAAG;AACpE,MAAA,OAAO,KAAA;AAAA,IACT;AAAA,EACF;AACA,EAAA,OAAO,IAAA;AACT;AAEA,SAAS,YAAA,CAAa,WAAA,EAAyB,WAAA,EAAyB;AACtE,EAAA,IAAI,uBAAA,EAAyB,KAAA;AAC7B,EAAA,IAAA,CAAA,MAAW,OAAA,GAAU,WAAA,CAAY,WAAA,EAAa;AAC5C,IAAA,GAAA,CACE,oDAAA,EAAgB,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,OAAO,CAAA,EAAG,WAAA,EAAa;AAAA,MACjE,iBAAA,EAAmB;AAAA,IACrB,CAAC,CAAA,EACD;AACA,MAAA,uBAAA,EAAyB,IAAA;AAAA,IAC3B;AACA,IAAA,GAAA,CACE,CAAC,oDAAA,EAAgB,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,OAAO,CAAA,EAAG,WAAA,EAAa;AAAA,MAClE,iBAAA,EAAmB;AAAA,IACrB,CAAC,CAAA,EACD;AACA,MAAA,OAAO,KAAA;AAAA,IACT;AAAA,EACF;AACA,EAAA,OAAO,sBAAA;AACT;AAEA,SAAS,YAAA,CAAa,OAAA,EAAkB,UAAA,EAAwB;AAC9D,EAAA,IAAI,OAAA,EAAS,KAAA;AACb,EAAA,IAAI,EAAA,EAAI,CAAA;AAER,EAAA,MAAM,SAAA,EAAW,wBAAA,OAAgB,CAAA;AACjC,EAAA,MAAM,SAAA,EAAW,wBAAA,UAAmB,CAAA;AACpC,EAAA,GAAA,CAAI,CAAC,aAAA,CAAc,QAAA,EAAU,QAAQ,CAAA,EAAG;AACtC,IAAA,OAAO,KAAA;AAAA,EACT;AACA,EAAA,IAAA,CAAK,CAAA,EAAG,EAAA,EAAI,UAAA,CAAW,WAAA,CAAY,OAAA,EAAS,CAAA,EAAG,CAAA,EAAA,EAAK;AAClD,IAAA,MAAM,SAAA,EAAW,WAAA;AAAA,MACf,UAAA,CAAW,WAAA,CAAY,CAAC,CAAA;AAAA,MACxB,UAAA,CAAW,WAAA,CAAY,EAAA,EAAI,CAAC;AAAA,IAC9B,CAAA;AACA,IAAA,GAAA,CACE,0DAAA,EAAwB,IAAA,EAAM,OAAA,EAAS,WAAA,EAAa,SAAS,CAAA,EAAG,OAAA,EAAS;AAAA,MACvE,cAAA,EAAgB;AAAA,IAClB,CAAC,CAAA,EACD;AACA,MAAA,OAAA,EAAS,IAAA;AACT,MAAA,KAAA;AAAA,IACF;AAAA,EACF;AACA,EAAA,OAAO,MAAA;AACT;AAWA,SAAS,YAAA,CACP,QAAA,EACA,QAAA,EACA;AAEA,EAAA,GAAA,CAAI,QAAA,CAAS,KAAA,IAAS,UAAA,GAAa,QAAA,CAAS,SAAA,IAAa,IAAA,EAAM;AAC7D,IAAA,OAAO,KAAA;AAAA,EACT;AACA,EAAA,GAAA,CAAI,QAAA,CAAS,KAAA,IAAS,UAAA,GAAa,QAAA,CAAS,SAAA,IAAa,IAAA,EAAM;AAC7D,IAAA,OAAO,KAAA;AAAA,EACT;AAEA,EAAA,MAAM,UAAA,EAAY,wBAAA,QAAiB,CAAA;AACnC,EAAA,MAAM,UAAA,EAAY,wBAAA,QAAiB,CAAA;AACnC,EAAA,GAAA,CAAI,CAAC,aAAA,CAAc,SAAA,EAAW,SAAS,CAAA,EAAG;AACxC,IAAA,OAAO,KAAA;AAAA,EACT;AAEA,EAAA,MAAM,OAAA,EAAS,gCAAA,QAAgB,CAAA,CAAE,WAAA;AACjC,EAAA,IAAA,CAAA,MAAW,KAAA,GAAQ,MAAA,EAAQ;AACzB,IAAA,IAAA,CAAA,MAAW,MAAA,GAAS,IAAA,EAAM;AACxB,MAAA,GAAA,CAAI,CAAC,0DAAA,KAAsB,EAAO,QAAQ,CAAA,EAAG;AAC3C,QAAA,OAAO,KAAA;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,EAAA,OAAO,IAAA;AACT;AAEA,SAAS,aAAA,CAAc,KAAA,EAAa,KAAA,EAAa;AAC/C,EAAA,GAAA,CAAI,KAAA,CAAM,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA,EAAG;AACvB,IAAA,OAAO,KAAA;AAAA,EACT;AACA,EAAA,GAAA,CAAI,KAAA,CAAM,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA,EAAG;AACvB,IAAA,OAAO,KAAA;AAAA,EACT;AACA,EAAA,GAAA,CAAI,KAAA,CAAM,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA,EAAG;AACvB,IAAA,OAAO,KAAA;AAAA,EACT;AACA,EAAA,GAAA,CAAI,KAAA,CAAM,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA,EAAG;AACvB,IAAA,OAAO,KAAA;AAAA,EACT;AACA,EAAA,OAAO,IAAA;AACT;AAUA,SAAS,aAAA,CAAc,KAAA,EAAiB,KAAA,EAAiB;AACvD,EAAA,OAAO,KAAA,CAAM,CAAC,EAAA,IAAM,KAAA,CAAM,CAAC,EAAA,GAAK,KAAA,CAAM,CAAC,EAAA,IAAM,KAAA,CAAM,CAAC,CAAA;AACtD;AAEA,SAAS,WAAA,CAAY,KAAA,EAAiB,KAAA,EAAiB;AACrD,EAAA,OAAO,CAAA,CAAE,KAAA,CAAM,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA,EAAA,EAAK,CAAA,EAAA,CAAI,KAAA,CAAM,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA,EAAA,EAAK,CAAC,CAAA;AAC9D;AAiBA,IAAO,8BAAA,EAAQ,eAAA;AD1Ff;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACF,wkBAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-boolean-contains/dist/cjs/index.cjs", "sourcesContent": [null, "import {\n  BBox,\n  Feature,\n  Geometry,\n  LineString,\n  MultiPoint,\n  MultiPolygon,\n  Point,\n  Polygon,\n} from \"geojson\";\nimport { bbox as calcBbox } from \"@turf/bbox\";\nimport { booleanPointInPolygon } from \"@turf/boolean-point-in-polygon\";\nimport { booleanPointOnLine as isPointOnLine } from \"@turf/boolean-point-on-line\";\nimport { getGeom } from \"@turf/invariant\";\n\n/**\n * Boolean-contains returns True if the second geometry is completely contained by the first geometry.\n * The interiors of both geometries must intersect and, the interior and boundary of the secondary (geometry b)\n * must not intersect the exterior of the primary (geometry a).\n * Boolean-contains returns the exact opposite result of the `@turf/boolean-within`.\n *\n * @function\n * @param {Geometry|Feature<any>} feature1 GeoJSON Feature or Geometry\n * @param {Geometry|Feature<any>} feature2 GeoJSON Feature or Geometry\n * @returns {boolean} true/false\n * @example\n * var line = turf.lineString([[1, 1], [1, 2], [1, 3], [1, 4]]);\n * var point = turf.point([1, 2]);\n *\n * turf.booleanContains(line, point);\n * //=true\n */\nfunction booleanContains(\n  feature1: Feature<any> | Geometry,\n  feature2: Feature<any> | Geometry\n) {\n  const geom1 = getGeom(feature1);\n  const geom2 = getGeom(feature2);\n  const type1 = geom1.type;\n  const type2 = geom2.type;\n  const coords1 = geom1.coordinates;\n  const coords2 = geom2.coordinates;\n\n  switch (type1) {\n    case \"Point\":\n      switch (type2) {\n        case \"Point\":\n          return compareCoords(coords1, coords2);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"MultiPoint\":\n      switch (type2) {\n        case \"Point\":\n          return isPointInMultiPoint(geom1, geom2);\n        case \"MultiPoint\":\n          return isMultiPointInMultiPoint(geom1, geom2);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"LineString\":\n      switch (type2) {\n        case \"Point\":\n          return isPointOnLine(geom2, geom1, { ignoreEndVertices: true });\n        case \"LineString\":\n          return isLineOnLine(geom1, geom2);\n        case \"MultiPoint\":\n          return isMultiPointOnLine(geom1, geom2);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"Polygon\":\n      switch (type2) {\n        case \"Point\":\n          return booleanPointInPolygon(geom2, geom1, { ignoreBoundary: true });\n        case \"LineString\":\n          return isLineInPoly(geom1, geom2);\n        case \"Polygon\":\n          return isPolyInPoly(geom1, geom2);\n        case \"MultiPoint\":\n          return isMultiPointInPoly(geom1, geom2);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"MultiPolygon\":\n      switch (type2) {\n        case \"Polygon\":\n          return isPolygonInMultiPolygon(geom1, geom2);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    default:\n      throw new Error(\"feature1 \" + type1 + \" geometry not supported\");\n  }\n}\n\nfunction isPolygonInMultiPolygon(multiPolygon: MultiPolygon, polygon: Polygon) {\n  return multiPolygon.coordinates.some((coords) =>\n    isPolyInPoly({ type: \"Polygon\", coordinates: coords }, polygon)\n  );\n}\n\nfunction isPointInMultiPoint(multiPoint: MultiPoint, pt: Point) {\n  let i;\n  let output = false;\n  for (i = 0; i < multiPoint.coordinates.length; i++) {\n    if (compareCoords(multiPoint.coordinates[i], pt.coordinates)) {\n      output = true;\n      break;\n    }\n  }\n  return output;\n}\n\nfunction isMultiPointInMultiPoint(\n  multiPoint1: MultiPoint,\n  multiPoint2: MultiPoint\n) {\n  for (const coord2 of multiPoint2.coordinates) {\n    let matchFound = false;\n    for (const coord1 of multiPoint1.coordinates) {\n      if (compareCoords(coord2, coord1)) {\n        matchFound = true;\n        break;\n      }\n    }\n    if (!matchFound) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction isMultiPointOnLine(lineString: LineString, multiPoint: MultiPoint) {\n  let haveFoundInteriorPoint = false;\n  for (const coord of multiPoint.coordinates) {\n    if (isPointOnLine(coord, lineString, { ignoreEndVertices: true })) {\n      haveFoundInteriorPoint = true;\n    }\n    if (!isPointOnLine(coord, lineString)) {\n      return false;\n    }\n  }\n  if (haveFoundInteriorPoint) {\n    return true;\n  }\n  return false;\n}\n\nfunction isMultiPointInPoly(polygon: Polygon, multiPoint: MultiPoint) {\n  for (const coord of multiPoint.coordinates) {\n    if (!booleanPointInPolygon(coord, polygon, { ignoreBoundary: true })) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction isLineOnLine(lineString1: LineString, lineString2: LineString) {\n  let haveFoundInteriorPoint = false;\n  for (const coords of lineString2.coordinates) {\n    if (\n      isPointOnLine({ type: \"Point\", coordinates: coords }, lineString1, {\n        ignoreEndVertices: true,\n      })\n    ) {\n      haveFoundInteriorPoint = true;\n    }\n    if (\n      !isPointOnLine({ type: \"Point\", coordinates: coords }, lineString1, {\n        ignoreEndVertices: false,\n      })\n    ) {\n      return false;\n    }\n  }\n  return haveFoundInteriorPoint;\n}\n\nfunction isLineInPoly(polygon: Polygon, linestring: LineString) {\n  let output = false;\n  let i = 0;\n\n  const polyBbox = calcBbox(polygon);\n  const lineBbox = calcBbox(linestring);\n  if (!doBBoxOverlap(polyBbox, lineBbox)) {\n    return false;\n  }\n  for (i; i < linestring.coordinates.length - 1; i++) {\n    const midPoint = getMidpoint(\n      linestring.coordinates[i],\n      linestring.coordinates[i + 1]\n    );\n    if (\n      booleanPointInPolygon({ type: \"Point\", coordinates: midPoint }, polygon, {\n        ignoreBoundary: true,\n      })\n    ) {\n      output = true;\n      break;\n    }\n  }\n  return output;\n}\n\n/**\n * Is Polygon2 in Polygon1\n * Only takes into account outer rings\n *\n * @private\n * @param {Geometry|Feature<Polygon>} feature1 Polygon1\n * @param {Geometry|Feature<Polygon>} feature2 Polygon2\n * @returns {boolean} true/false\n */\nfunction isPolyInPoly(\n  feature1: Feature<Polygon> | Polygon,\n  feature2: Feature<Polygon> | Polygon\n) {\n  // Handle Nulls\n  if (feature1.type === \"Feature\" && feature1.geometry === null) {\n    return false;\n  }\n  if (feature2.type === \"Feature\" && feature2.geometry === null) {\n    return false;\n  }\n\n  const poly1Bbox = calcBbox(feature1);\n  const poly2Bbox = calcBbox(feature2);\n  if (!doBBoxOverlap(poly1Bbox, poly2Bbox)) {\n    return false;\n  }\n\n  const coords = getGeom(feature2).coordinates;\n  for (const ring of coords) {\n    for (const coord of ring) {\n      if (!booleanPointInPolygon(coord, feature1)) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\nfunction doBBoxOverlap(bbox1: BBox, bbox2: BBox) {\n  if (bbox1[0] > bbox2[0]) {\n    return false;\n  }\n  if (bbox1[2] < bbox2[2]) {\n    return false;\n  }\n  if (bbox1[1] > bbox2[1]) {\n    return false;\n  }\n  if (bbox1[3] < bbox2[3]) {\n    return false;\n  }\n  return true;\n}\n\n/**\n * compareCoords\n *\n * @private\n * @param {Position} pair1 point [x,y]\n * @param {Position} pair2 point [x,y]\n * @returns {boolean} true/false if coord pairs match\n */\nfunction compareCoords(pair1: number[], pair2: number[]) {\n  return pair1[0] === pair2[0] && pair1[1] === pair2[1];\n}\n\nfunction getMidpoint(pair1: number[], pair2: number[]) {\n  return [(pair1[0] + pair2[0]) / 2, (pair1[1] + pair2[1]) / 2];\n}\n\nexport {\n  booleanContains,\n  isPolygonInMultiPolygon,\n  isPointInMultiPoint,\n  isMultiPointInMultiPoint,\n  isMultiPointOnLine,\n  isMultiPointInPoly,\n  isLineOnLine,\n  isLineInPoly,\n  isPolyInPoly,\n  doBBoxOverlap,\n  compareCoords,\n  getMidpoint,\n};\n\nexport default booleanContains;\n"]}