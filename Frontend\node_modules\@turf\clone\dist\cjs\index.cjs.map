{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-clone/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACeA,SAAS,KAAA,CAA4B,OAAA,EAAe;AAClD,EAAA,GAAA,CAAI,CAAC,OAAA,EAAS;AACZ,IAAA,MAAM,IAAI,KAAA,CAAM,qBAAqB,CAAA;AAAA,EACvC;AAEA,EAAA,OAAA,CAAQ,OAAA,CAAQ,IAAA,EAAM;AAAA,IACpB,KAAK,SAAA;AACH,MAAA,OAAO,YAAA,CAAa,OAAO,CAAA;AAAA,IAC7B,KAAK,mBAAA;AACH,MAAA,OAAO,sBAAA,CAAuB,OAAO,CAAA;AAAA,IACvC,KAAK,OAAA;AAAA,IACL,KAAK,YAAA;AAAA,IACL,KAAK,SAAA;AAAA,IACL,KAAK,YAAA;AAAA,IACL,KAAK,iBAAA;AAAA,IACL,KAAK,cAAA;AAAA,IACL,KAAK,oBAAA;AACH,MAAA,OAAO,aAAA,CAAc,OAAO,CAAA;AAAA,IAC9B,OAAA;AACE,MAAA,MAAM,IAAI,KAAA,CAAM,sBAAsB,CAAA;AAAA,EAC1C;AACF;AASA,SAAS,YAAA,CAAa,OAAA,EAAc;AAClC,EAAA,MAAM,OAAA,EAAc,EAAE,IAAA,EAAM,UAAU,CAAA;AAEtC,EAAA,MAAA,CAAO,IAAA,CAAK,OAAO,CAAA,CAAE,OAAA,CAAQ,CAAC,GAAA,EAAA,GAAQ;AACpC,IAAA,OAAA,CAAQ,GAAA,EAAK;AAAA,MACX,KAAK,MAAA;AAAA,MACL,KAAK,YAAA;AAAA,MACL,KAAK,UAAA;AACH,QAAA,MAAA;AAAA,MACF,OAAA;AACE,QAAA,MAAA,CAAO,GAAG,EAAA,EAAI,OAAA,CAAQ,GAAG,CAAA;AAAA,IAC7B;AAAA,EACF,CAAC,CAAA;AAED,EAAA,MAAA,CAAO,WAAA,EAAa,eAAA,CAAgB,OAAA,CAAQ,UAAU,CAAA;AACtD,EAAA,GAAA,CAAI,OAAA,CAAQ,SAAA,GAAY,IAAA,EAAM;AAC5B,IAAA,MAAA,CAAO,SAAA,EAAW,IAAA;AAAA,EACpB,EAAA,KAAO;AACL,IAAA,MAAA,CAAO,SAAA,EAAW,aAAA,CAAc,OAAA,CAAQ,QAAQ,CAAA;AAAA,EAClD;AACA,EAAA,OAAO,MAAA;AACT;AASA,SAAS,eAAA,CAAgB,UAAA,EAA+B;AACtD,EAAA,MAAM,OAAA,EAAiC,CAAC,CAAA;AACxC,EAAA,GAAA,CAAI,CAAC,UAAA,EAAY;AACf,IAAA,OAAO,MAAA;AAAA,EACT;AACA,EAAA,MAAA,CAAO,IAAA,CAAK,UAAU,CAAA,CAAE,OAAA,CAAQ,CAAC,GAAA,EAAA,GAAQ;AACvC,IAAA,MAAM,MAAA,EAAQ,UAAA,CAAW,GAAG,CAAA;AAC5B,IAAA,GAAA,CAAI,OAAO,MAAA,IAAU,QAAA,EAAU;AAC7B,MAAA,GAAA,CAAI,MAAA,IAAU,IAAA,EAAM;AAElB,QAAA,MAAA,CAAO,GAAG,EAAA,EAAI,IAAA;AAAA,MAChB,EAAA,KAAA,GAAA,CAAW,KAAA,CAAM,OAAA,CAAQ,KAAK,CAAA,EAAG;AAE/B,QAAA,MAAA,CAAO,GAAG,EAAA,EAAI,KAAA,CAAM,GAAA,CAAI,CAAC,IAAA,EAAA,GAAS;AAChC,UAAA,OAAO,IAAA;AAAA,QACT,CAAC,CAAA;AAAA,MACH,EAAA,KAAO;AAEL,QAAA,MAAA,CAAO,GAAG,EAAA,EAAI,eAAA,CAAgB,KAAK,CAAA;AAAA,MACrC;AAAA,IACF,EAAA,KAAO;AACL,MAAA,MAAA,CAAO,GAAG,EAAA,EAAI,KAAA;AAAA,IAChB;AAAA,EACF,CAAC,CAAA;AACD,EAAA,OAAO,MAAA;AACT;AASA,SAAS,sBAAA,CAAuB,OAAA,EAAc;AAC5C,EAAA,MAAM,OAAA,EAAc,EAAE,IAAA,EAAM,oBAAoB,CAAA;AAGhD,EAAA,MAAA,CAAO,IAAA,CAAK,OAAO,CAAA,CAAE,OAAA,CAAQ,CAAC,GAAA,EAAA,GAAQ;AACpC,IAAA,OAAA,CAAQ,GAAA,EAAK;AAAA,MACX,KAAK,MAAA;AAAA,MACL,KAAK,UAAA;AACH,QAAA,MAAA;AAAA,MACF,OAAA;AACE,QAAA,MAAA,CAAO,GAAG,EAAA,EAAI,OAAA,CAAQ,GAAG,CAAA;AAAA,IAC7B;AAAA,EACF,CAAC,CAAA;AAED,EAAA,MAAA,CAAO,SAAA,EAAW,OAAA,CAAQ,QAAA,CAAS,GAAA,CAAI,CAAC,OAAA,EAAA,GAA0B;AAChE,IAAA,OAAO,YAAA,CAAa,OAAO,CAAA;AAAA,EAC7B,CAAC,CAAA;AACD,EAAA,OAAO,MAAA;AACT;AASA,SAAS,aAAA,CAAc,QAAA,EAAe;AACpC,EAAA,MAAM,KAAA,EAAY,EAAE,IAAA,EAAM,QAAA,CAAS,KAAK,CAAA;AACxC,EAAA,GAAA,CAAI,QAAA,CAAS,IAAA,EAAM;AACjB,IAAA,IAAA,CAAK,KAAA,EAAO,QAAA,CAAS,IAAA;AAAA,EACvB;AAEA,EAAA,GAAA,CAAI,QAAA,CAAS,KAAA,IAAS,oBAAA,EAAsB;AAC1C,IAAA,IAAA,CAAK,WAAA,EAAa,QAAA,CAAS,UAAA,CAAW,GAAA,CAAI,CAAC,CAAA,EAAA,GAAW;AACpD,MAAA,OAAO,aAAA,CAAc,CAAC,CAAA;AAAA,IACxB,CAAC,CAAA;AACD,IAAA,OAAO,IAAA;AAAA,EACT;AACA,EAAA,IAAA,CAAK,YAAA,EAAc,SAAA,CAAU,QAAA,CAAS,WAAW,CAAA;AACjD,EAAA,OAAO,IAAA;AACT;AASA,SAAS,SAAA,CAAqB,MAAA,EAAc;AAC1C,EAAA,MAAM,OAAA,EAAc,MAAA;AACpB,EAAA,GAAA,CAAI,OAAO,MAAA,CAAO,CAAC,EAAA,IAAM,QAAA,EAAU;AACjC,IAAA,OAAO,MAAA,CAAO,KAAA,CAAM,CAAA;AAAA,EACtB;AACA,EAAA,OAAO,MAAA,CAAO,GAAA,CAAI,CAAC,KAAA,EAAA,GAAe;AAChC,IAAA,OAAO,SAAA,CAAU,KAAK,CAAA;AAAA,EACxB,CAAC,CAAA;AACH;AAGA,IAAO,mBAAA,EAAQ,KAAA;ADjEf;AACE;AACA;AACA;AACF,uGAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-clone/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, GeoJsonProperties } from \"geojson\";\nimport { AllGeoJSON } from \"@turf/helpers\";\n\n/**\n * Returns a cloned copy of the passed GeoJSON Object, including possible 'Foreign Members'.\n * ~3-5x faster than the common JSON.parse + JSON.stringify combo method.\n *\n * @function\n * @param {GeoJSON} geojson GeoJSON Object\n * @returns {GeoJSON} cloned GeoJSON Object\n * @example\n * var line = turf.lineString([[-74, 40], [-78, 42], [-82, 35]], {color: 'red'});\n *\n * var lineCloned = turf.clone(line);\n */\nfunction clone<T extends AllGeoJSON>(geojson: T): T {\n  if (!geojson) {\n    throw new Error(\"geojson is required\");\n  }\n\n  switch (geojson.type) {\n    case \"Feature\":\n      return cloneFeature(geojson);\n    case \"FeatureCollection\":\n      return cloneFeatureCollection(geojson);\n    case \"Point\":\n    case \"LineString\":\n    case \"Polygon\":\n    case \"MultiPoint\":\n    case \"MultiLineString\":\n    case \"MultiPolygon\":\n    case \"GeometryCollection\":\n      return cloneGeometry(geojson);\n    default:\n      throw new Error(\"unknown GeoJSON type\");\n  }\n}\n\n/**\n * Clone Feature\n *\n * @private\n * @param {Feature<any>} geojson GeoJSON Feature\n * @returns {Feature<any>} cloned Feature\n */\nfunction cloneFeature(geojson: any) {\n  const cloned: any = { type: \"Feature\" };\n  // Preserve Foreign Members\n  Object.keys(geojson).forEach((key) => {\n    switch (key) {\n      case \"type\":\n      case \"properties\":\n      case \"geometry\":\n        return;\n      default:\n        cloned[key] = geojson[key];\n    }\n  });\n  // Add properties & geometry last\n  cloned.properties = cloneProperties(geojson.properties);\n  if (geojson.geometry == null) {\n    cloned.geometry = null;\n  } else {\n    cloned.geometry = cloneGeometry(geojson.geometry);\n  }\n  return cloned;\n}\n\n/**\n * Clone Properties\n *\n * @private\n * @param {Object} properties GeoJSON Properties\n * @returns {Object} cloned Properties\n */\nfunction cloneProperties(properties: GeoJsonProperties) {\n  const cloned: { [key: string]: any } = {};\n  if (!properties) {\n    return cloned;\n  }\n  Object.keys(properties).forEach((key) => {\n    const value = properties[key];\n    if (typeof value === \"object\") {\n      if (value === null) {\n        // handle null\n        cloned[key] = null;\n      } else if (Array.isArray(value)) {\n        // handle Array\n        cloned[key] = value.map((item) => {\n          return item;\n        });\n      } else {\n        // handle generic Object\n        cloned[key] = cloneProperties(value);\n      }\n    } else {\n      cloned[key] = value;\n    }\n  });\n  return cloned;\n}\n\n/**\n * Clone Feature Collection\n *\n * @private\n * @param {FeatureCollection<any>} geojson GeoJSON Feature Collection\n * @returns {FeatureCollection<any>} cloned Feature Collection\n */\nfunction cloneFeatureCollection(geojson: any) {\n  const cloned: any = { type: \"FeatureCollection\" };\n\n  // Preserve Foreign Members\n  Object.keys(geojson).forEach((key) => {\n    switch (key) {\n      case \"type\":\n      case \"features\":\n        return;\n      default:\n        cloned[key] = geojson[key];\n    }\n  });\n  // Add features\n  cloned.features = geojson.features.map((feature: Feature<any>) => {\n    return cloneFeature(feature);\n  });\n  return cloned;\n}\n\n/**\n * Clone Geometry\n *\n * @private\n * @param {Geometry<any>} geometry GeoJSON Geometry\n * @returns {Geometry<any>} cloned Geometry\n */\nfunction cloneGeometry(geometry: any) {\n  const geom: any = { type: geometry.type };\n  if (geometry.bbox) {\n    geom.bbox = geometry.bbox;\n  }\n\n  if (geometry.type === \"GeometryCollection\") {\n    geom.geometries = geometry.geometries.map((g: any) => {\n      return cloneGeometry(g);\n    });\n    return geom;\n  }\n  geom.coordinates = deepSlice(geometry.coordinates);\n  return geom;\n}\n\n/**\n * Deep Slice coordinates\n *\n * @private\n * @param {Coordinates} coords Coordinates\n * @returns {Coordinates} all coordinates sliced\n */\nfunction deepSlice<C = any[]>(coords: C): C {\n  const cloned: any = coords;\n  if (typeof cloned[0] !== \"object\") {\n    return cloned.slice();\n  }\n  return cloned.map((coord: any) => {\n    return deepSlice(coord);\n  });\n}\n\nexport { clone, cloneProperties };\nexport default clone;\n"]}