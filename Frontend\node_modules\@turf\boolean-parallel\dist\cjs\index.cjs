"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _cleancoords = require('@turf/clean-coords');
var _linesegment = require('@turf/line-segment');
var _rhumbbearing = require('@turf/rhumb-bearing');
var _helpers = require('@turf/helpers');
function booleanParallel(line1, line2) {
  if (!line1) throw new Error("line1 is required");
  if (!line2) throw new Error("line2 is required");
  var type1 = getType(line1, "line1");
  if (type1 !== "LineString") throw new Error("line1 must be a LineString");
  var type2 = getType(line2, "line2");
  if (type2 !== "LineString") throw new Error("line2 must be a LineString");
  var segments1 = _linesegment.lineSegment.call(void 0, _cleancoords.cleanCoords.call(void 0, line1)).features;
  var segments2 = _linesegment.lineSegment.call(void 0, _cleancoords.cleanCoords.call(void 0, line2)).features;
  for (var i = 0; i < segments1.length; i++) {
    var segment1 = segments1[i].geometry.coordinates;
    if (!segments2[i]) break;
    var segment2 = segments2[i].geometry.coordinates;
    if (!isParallel(segment1, segment2)) return false;
  }
  return true;
}
function isParallel(segment1, segment2) {
  var slope1 = _helpers.bearingToAzimuth.call(void 0, _rhumbbearing.rhumbBearing.call(void 0, segment1[0], segment1[1]));
  var slope2 = _helpers.bearingToAzimuth.call(void 0, _rhumbbearing.rhumbBearing.call(void 0, segment2[0], segment2[1]));
  return slope1 === slope2 || (slope2 - slope1) % 180 === 0;
}
function getType(geojson, name) {
  if (geojson.geometry && geojson.geometry.type)
    return geojson.geometry.type;
  if (geojson.type) return geojson.type;
  throw new Error("Invalid GeoJSON object for " + name);
}
var turf_boolean_parallel_default = booleanParallel;



exports.booleanParallel = booleanParallel; exports.default = turf_boolean_parallel_default;
//# sourceMappingURL=index.cjs.map