{"authors": "<PERSON> <https://github.com/gcroteau>, <PERSON> <https://github.com/Denis<PERSON>>", "definitionFilename": "index.d.ts", "libraryDependencies": ["g<PERSON><PERSON><PERSON>"], "moduleDependencies": [], "libraryMajorVersion": "3", "libraryMinorVersion": "5", "libraryName": "Turf 3.5.2", "typingsPackageName": "turf", "projectName": "http://turfjs.org/", "sourceRepoURL": "https://www.github.com/DefinitelyTyped/DefinitelyTyped", "sourceBranch": "types-2.0", "kind": "MultipleModules", "globals": ["TemplateType", "TemplateUnits", "turf"], "declaredModules": ["turf", "@turf/turf", "@turf/collect", "@turf/along", "@turf/area", "@turf/bbox-polygon", "@turf/bearing", "@turf/center", "@turf/centroid", "@turf/destination", "@turf/distance", "@turf/envelope", "@turf/line-distance", "@turf/midpoint", "@turf/point-on-surface", "@turf/square", "@turf/bezier", "@turf/buffer", "@turf/concave", "@turf/convex", "@turf/difference", "@turf/intersect", "@turf/simplify", "@turf/union", "@turf/combine", "@turf/explode", "@turf/flip", "@turf/kinks", "@turf/line-slice", "@turf/point-on-line", "@turf/helpers", "@turf/random", "@turf/sample", "@turf/isolines", "@turf/planepoint", "@turf/tin", "@turf/inside", "@turf/tag", "@turf/within", "@turf/hex-grid", "@turf/point-grid", "@turf/square-grid", "@turf/triangle-grid", "@turf/nearest", "@turf/bbox", "@turf/circle", "@turf/geojsonType", "@turf/propReduce", "@turf/coordAll", "@turf/tesselate"], "files": ["index.d.ts"], "hasPackageJson": false, "contentHash": "83b843a59d6edf387192ad9c89cf6da5102fee755da15f8f4326a15b9851c7cc"}