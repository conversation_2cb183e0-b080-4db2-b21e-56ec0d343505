# @turf/boolean-point-in-polygon

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## booleanPointInPolygon

Takes a [Point][1] and a [Polygon][2] or [MultiPolygon][3] and determines if the point
resides inside the polygon. The polygon can be convex or concave. The function accounts for holes.

### Parameters

*   `point` **[Coord][4]** input point
*   `polygon` **[Feature][5]<([Polygon][2] | [MultiPolygon][3])>** input polygon or multipolygon
*   `options` **[Object][6]** Optional parameters (optional, default `{}`)

    *   `options.ignoreBoundary` **[boolean][7]** True if polygon boundary should be ignored when determining if
        the point is inside the polygon otherwise false. (optional, default `false`)

### Examples

```javascript
var pt = turf.point([-77, 44]);
var poly = turf.polygon([[
  [-81, 41],
  [-81, 47],
  [-72, 47],
  [-72, 41],
  [-81, 41]
]]);

turf.booleanPointInPolygon(pt, poly);
//= true
```

Returns **[boolean][7]** `true` if the Point is inside the Polygon; `false` if the Point is not inside the Polygon

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.7

[4]: https://tools.ietf.org/html/rfc7946#section-3.1.1

[5]: https://tools.ietf.org/html/rfc7946#section-3.2

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/boolean-point-in-polygon
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
