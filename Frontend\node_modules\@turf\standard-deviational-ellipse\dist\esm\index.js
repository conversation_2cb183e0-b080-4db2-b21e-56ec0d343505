// index.ts
import { coordAll, featureEach } from "@turf/meta";
import { getCoords } from "@turf/invariant";
import { featureCollection, isObject, isNumber } from "@turf/helpers";
import { centerMean } from "@turf/center-mean";
import { pointsWithinPolygon } from "@turf/points-within-polygon";
import { ellipse } from "@turf/ellipse";
function standardDeviationalEllipse(points, options) {
  var _a;
  options = options || {};
  if (!isObject(options)) throw new Error("options is invalid");
  const steps = options.steps || 64;
  const weightTerm = options.weight;
  const properties = options.properties || {};
  if (!isNumber(steps)) throw new Error("steps must be a number");
  if (!isObject(properties)) throw new Error("properties must be a number");
  const numberOfFeatures = coordAll(points).length;
  const meanCenter = centerMean(points, { weight: weightTerm });
  let xDeviationSquaredSum = 0;
  let yDeviationSquaredSum = 0;
  let xyDeviationSum = 0;
  featureEach(points, function(point) {
    var _a2;
    const weight = weightTerm ? ((_a2 = point.properties) == null ? void 0 : _a2[weightTerm]) || 1 : 1;
    const deviation = getDeviations(getCoords(point), getCoords(meanCenter));
    xDeviationSquaredSum += Math.pow(deviation.x, 2) * weight;
    yDeviationSquaredSum += Math.pow(deviation.y, 2) * weight;
    xyDeviationSum += deviation.x * deviation.y * weight;
  });
  const bigA = xDeviationSquaredSum - yDeviationSquaredSum;
  const bigB = Math.sqrt(Math.pow(bigA, 2) + 4 * Math.pow(xyDeviationSum, 2));
  const bigC = 2 * xyDeviationSum;
  const theta = Math.atan((bigA + bigB) / bigC);
  const thetaDeg = theta * 180 / Math.PI;
  let sigmaXsum = 0;
  let sigmaYsum = 0;
  let weightsum = 0;
  featureEach(points, function(point) {
    var _a2;
    const weight = weightTerm ? ((_a2 = point.properties) == null ? void 0 : _a2[weightTerm]) || 1 : 1;
    const deviation = getDeviations(getCoords(point), getCoords(meanCenter));
    sigmaXsum += Math.pow(
      deviation.x * Math.cos(theta) - deviation.y * Math.sin(theta),
      2
    ) * weight;
    sigmaYsum += Math.pow(
      deviation.x * Math.sin(theta) + deviation.y * Math.cos(theta),
      2
    ) * weight;
    weightsum += weight;
  });
  const sigmaX = Math.sqrt(2 * sigmaXsum / weightsum);
  const sigmaY = Math.sqrt(2 * sigmaYsum / weightsum);
  const theEllipse = ellipse(meanCenter, sigmaX, sigmaY, {
    units: "degrees",
    angle: thetaDeg,
    steps,
    properties
  });
  const pointsWithinEllipse = pointsWithinPolygon(
    points,
    featureCollection([theEllipse])
  );
  const standardDeviationalEllipseProperties = {
    meanCenterCoordinates: getCoords(meanCenter),
    semiMajorAxis: sigmaX,
    semiMinorAxis: sigmaY,
    numberOfFeatures,
    angle: thetaDeg,
    percentageWithinEllipse: 100 * coordAll(pointsWithinEllipse).length / numberOfFeatures
  };
  theEllipse.properties = (_a = theEllipse.properties) != null ? _a : {};
  theEllipse.properties.standardDeviationalEllipse = standardDeviationalEllipseProperties;
  return theEllipse;
}
function getDeviations(coordinates, center) {
  return {
    x: coordinates[0] - center[0],
    y: coordinates[1] - center[1]
  };
}
var turf_standard_deviational_ellipse_default = standardDeviationalEllipse;
export {
  turf_standard_deviational_ellipse_default as default,
  standardDeviationalEllipse
};
//# sourceMappingURL=index.js.map