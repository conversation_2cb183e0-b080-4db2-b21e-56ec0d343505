{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Feature, GeoJsonProperties, Point, Position } from \"geojson\";\nimport { convex } from \"@turf/convex\";\nimport { centroid } from \"@turf/centroid\";\nimport { point } from \"@turf/helpers\";\nimport { getType, getCoord } from \"@turf/invariant\";\nimport { coordEach } from \"@turf/meta\";\n\n/**\n * Takes any {@link Feature} or a {@link FeatureCollection} and returns its [center of mass](https://en.wikipedia.org/wiki/Center_of_mass) using this formula: [Centroid of Polygon](https://en.wikipedia.org/wiki/Centroid#Centroid_of_polygon).\n *\n * @function\n * @param {GeoJSON} geojson GeoJSON to be centered\n * @param {Object} [options={}] Optional Parameters\n * @param {Object} [options.properties={}] Translate Properties to Feature\n * @returns {Feature<Point>} the center of mass\n * @example\n * var polygon = turf.polygon([[[-81, 41], [-88, 36], [-84, 31], [-80, 33], [-77, 39], [-81, 41]]]);\n *\n * var center = turf.centerOfMass(polygon);\n *\n * //addToMap\n * var addToMap = [polygon, center]\n */\nfunction centerOfMass<P extends GeoJsonProperties = GeoJsonProperties>(\n  geojson: any,\n  options: {\n    properties?: P;\n  } = {}\n): Feature<Point, P> {\n  switch (getType(geojson)) {\n    case \"Point\":\n      return point(getCoord(geojson), options.properties);\n    case \"Polygon\":\n      var coords: Position[] = [];\n      coordEach(geojson, function (coord) {\n        coords.push(coord);\n      });\n\n      // First, we neutralize the feature (set it around coordinates [0,0]) to prevent rounding errors\n      // We take any point to translate all the points around 0\n      var centre = centroid(geojson, { properties: options.properties });\n      var translation = centre.geometry.coordinates;\n      var sx = 0;\n      var sy = 0;\n      var sArea = 0;\n      var i, pi, pj, xi, xj, yi, yj, a;\n\n      var neutralizedPoints = coords.map(function (point) {\n        return [point[0] - translation[0], point[1] - translation[1]];\n      });\n\n      for (i = 0; i < coords.length - 1; i++) {\n        // pi is the current point\n        pi = neutralizedPoints[i];\n        xi = pi[0];\n        yi = pi[1];\n\n        // pj is the next point (pi+1)\n        pj = neutralizedPoints[i + 1];\n        xj = pj[0];\n        yj = pj[1];\n\n        // a is the common factor to compute the signed area and the final coordinates\n        a = xi * yj - xj * yi;\n\n        // sArea is the sum used to compute the signed area\n        sArea += a;\n\n        // sx and sy are the sums used to compute the final coordinates\n        sx += (xi + xj) * a;\n        sy += (yi + yj) * a;\n      }\n\n      // Shape has no area: fallback on turf.centroid\n      if (sArea === 0) {\n        return centre;\n      } else {\n        // Compute the signed area, and factorize 1/6A\n        var area = sArea * 0.5;\n        var areaFactor = 1 / (6 * area);\n\n        // Compute the final coordinates, adding back the values that have been neutralized\n        return point(\n          [translation[0] + areaFactor * sx, translation[1] + areaFactor * sy],\n          options.properties\n        );\n      }\n    default:\n      // Not a polygon: Compute the convex hull and work with that\n      var hull = convex(geojson);\n\n      if (hull) return centerOfMass(hull, { properties: options.properties });\n      // Hull is empty: fallback on the centroid\n      else return centroid(geojson, { properties: options.properties });\n  }\n}\n\nexport { centerOfMass };\nexport default centerOfMass;\n"], "mappings": ";AACA,SAAS,cAAc;AACvB,SAAS,gBAAgB;AACzB,SAAS,aAAa;AACtB,SAAS,SAAS,gBAAgB;AAClC,SAAS,iBAAiB;AAkB1B,SAAS,aACP,SACA,UAEI,CAAC,GACc;AACnB,UAAQ,QAAQ,OAAO,GAAG;AAAA,IACxB,KAAK;AACH,aAAO,MAAM,SAAS,OAAO,GAAG,QAAQ,UAAU;AAAA,IACpD,KAAK;AACH,UAAI,SAAqB,CAAC;AAC1B,gBAAU,SAAS,SAAU,OAAO;AAClC,eAAO,KAAK,KAAK;AAAA,MACnB,CAAC;AAID,UAAI,SAAS,SAAS,SAAS,EAAE,YAAY,QAAQ,WAAW,CAAC;AACjE,UAAI,cAAc,OAAO,SAAS;AAClC,UAAI,KAAK;AACT,UAAI,KAAK;AACT,UAAI,QAAQ;AACZ,UAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAE/B,UAAI,oBAAoB,OAAO,IAAI,SAAUA,QAAO;AAClD,eAAO,CAACA,OAAM,CAAC,IAAI,YAAY,CAAC,GAAGA,OAAM,CAAC,IAAI,YAAY,CAAC,CAAC;AAAA,MAC9D,CAAC;AAED,WAAK,IAAI,GAAG,IAAI,OAAO,SAAS,GAAG,KAAK;AAEtC,aAAK,kBAAkB,CAAC;AACxB,aAAK,GAAG,CAAC;AACT,aAAK,GAAG,CAAC;AAGT,aAAK,kBAAkB,IAAI,CAAC;AAC5B,aAAK,GAAG,CAAC;AACT,aAAK,GAAG,CAAC;AAGT,YAAI,KAAK,KAAK,KAAK;AAGnB,iBAAS;AAGT,eAAO,KAAK,MAAM;AAClB,eAAO,KAAK,MAAM;AAAA,MACpB;AAGA,UAAI,UAAU,GAAG;AACf,eAAO;AAAA,MACT,OAAO;AAEL,YAAI,OAAO,QAAQ;AACnB,YAAI,aAAa,KAAK,IAAI;AAG1B,eAAO;AAAA,UACL,CAAC,YAAY,CAAC,IAAI,aAAa,IAAI,YAAY,CAAC,IAAI,aAAa,EAAE;AAAA,UACnE,QAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAEE,UAAI,OAAO,OAAO,OAAO;AAEzB,UAAI,KAAM,QAAO,aAAa,MAAM,EAAE,YAAY,QAAQ,WAAW,CAAC;AAAA,UAEjE,QAAO,SAAS,SAAS,EAAE,YAAY,QAAQ,WAAW,CAAC;AAAA,EACpE;AACF;AAGA,IAAO,8BAAQ;", "names": ["point"]}