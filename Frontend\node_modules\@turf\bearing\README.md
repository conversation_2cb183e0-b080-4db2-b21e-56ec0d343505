# @turf/bearing

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## bearing

Takes two [points][1] and finds the geographic bearing between them,
i.e. the angle measured in degrees from the north line (0 degrees)

### Parameters

*   `start` **[Coord][2]** starting Point
*   `end` **[Coord][2]** ending Point
*   `options` **[Object][3]** Optional parameters (optional, default `{}`)

    *   `options.final` **[boolean][4]** calculates the final bearing if true (optional, default `false`)

### Examples

```javascript
var point1 = turf.point([-75.343, 39.984]);
var point2 = turf.point([-75.534, 39.123]);

var bearing = turf.bearing(point1, point2);

//addToMap
var addToMap = [point1, point2]
point1.properties['marker-color'] = '#f00'
point2.properties['marker-color'] = '#0f0'
point1.properties.bearing = bearing
```

Returns **[number][5]** bearing in decimal degrees, between -180 and 180 degrees (positive clockwise)

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.1

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/bearing
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
