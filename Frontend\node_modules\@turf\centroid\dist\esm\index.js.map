{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Feature, GeoJsonProperties, Point } from \"geojson\";\nimport { point, AllGeoJSON } from \"@turf/helpers\";\nimport { coordEach } from \"@turf/meta\";\n\n/**\n * Computes the centroid as the mean of all vertices within the object.\n *\n * @function\n * @param {GeoJSON} geojson GeoJSON to be centered\n * @param {Object} [options={}] Optional Parameters\n * @param {Object} [options.properties={}] an Object that is used as the {@link Feature}'s properties\n * @returns {Feature<Point>} the centroid of the input object\n * @example\n * var polygon = turf.polygon([[[-81, 41], [-88, 36], [-84, 31], [-80, 33], [-77, 39], [-81, 41]]]);\n *\n * var centroid = turf.centroid(polygon);\n *\n * //addToMap\n * var addToMap = [polygon, centroid]\n */\nfunction centroid<P extends GeoJsonProperties = GeoJsonProperties>(\n  geojson: AllGeoJSON,\n  options: {\n    properties?: P;\n  } = {}\n): Feature<Point, P> {\n  let xSum = 0;\n  let ySum = 0;\n  let len = 0;\n  coordEach(\n    geojson,\n    function (coord) {\n      xSum += coord[0];\n      ySum += coord[1];\n      len++;\n    },\n    true\n  );\n  return point([xSum / len, ySum / len], options.properties);\n}\n\nexport { centroid };\nexport default centroid;\n"], "mappings": ";AACA,SAAS,aAAyB;AAClC,SAAS,iBAAiB;AAkB1B,SAAS,SACP,SACA,UAEI,CAAC,GACc;AACnB,MAAI,OAAO;AACX,MAAI,OAAO;AACX,MAAI,MAAM;AACV;AAAA,IACE;AAAA,IACA,SAAU,OAAO;AACf,cAAQ,MAAM,CAAC;AACf,cAAQ,MAAM,CAAC;AACf;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,SAAO,MAAM,CAAC,OAAO,KAAK,OAAO,GAAG,GAAG,QAAQ,UAAU;AAC3D;AAGA,IAAO,wBAAQ;", "names": []}