{"name": "rbush", "version": "2.0.2", "description": "High-performance 2D spatial index for rectangles (based on R*-tree with bulk loading and bulk insertion algorithms)", "homepage": "https://github.com/mourner/rbush", "repository": {"type": "git", "url": "git://github.com/mourner/rbush.git"}, "keywords": ["spatial", "tree", "search", "rectangle", "index", "math"], "author": "<PERSON>", "license": "MIT", "main": "index.js", "jsdelivr": "rbush.js", "unpkg": "rbush.js", "devDependencies": {"benchmark": "^2.1.4", "browserify": "^14.5.0", "eslint": "^4.13.1", "eslint-config-mourner": "^2.0.3", "faucet": "0.0.1", "istanbul": "~0.4.5", "tape": "^4.8.0", "uglify-js": "^3.2.2"}, "scripts": {"test": "eslint index.js test/test.js && node test/test.js | faucet", "perf": "node ./bench/perf.js", "cov": "istanbul cover test/test.js -x test/test.js", "build": "browserify index.js -s rbush -o rbush.js", "build-min": "browserify index.js -s rbush | uglifyjs -c warnings=false -m > rbush.min.js", "prepare": "npm run build && npm run build-min"}, "files": ["rbush.js", "rbush.min.js"], "eslintConfig": {"extends": "mourner", "rules": {"new-cap": 0, "consistent-return": 0}}, "dependencies": {"quickselect": "^1.0.1"}}