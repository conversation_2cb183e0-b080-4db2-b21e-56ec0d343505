{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Polygon, MultiPolygon, Feature, FeatureCollection } from \"geojson\";\nimport * as polyclip from \"polyclip-ts\";\nimport { polygon, multiPolygon } from \"@turf/helpers\";\nimport { geomEach } from \"@turf/meta\";\n\n/**\n * Finds the difference between multiple {@link Polygon|polygons} by clipping the subsequent polygon from the first.\n *\n * @function\n * @param {FeatureCollection<Polygon|MultiPolygon>} features input Polygon features\n * @returns {Feature<Polygon|MultiPolygon>|null} a Polygon or MultiPolygon feature showing the area of `polygon1` excluding the area of `polygon2` (if empty returns `null`)\n * @example\n * var polygon1 = turf.polygon([[\n *   [128, -26],\n *   [141, -26],\n *   [141, -21],\n *   [128, -21],\n *   [128, -26]\n * ]], {\n *   \"fill\": \"#F00\",\n *   \"fill-opacity\": 0.1\n * });\n * var polygon2 = turf.polygon([[\n *   [126, -28],\n *   [140, -28],\n *   [140, -20],\n *   [126, -20],\n *   [126, -28]\n * ]], {\n *   \"fill\": \"#00F\",\n *   \"fill-opacity\": 0.1\n * });\n *\n * var difference = turf.difference(turf.featureCollection([polygon1, polygon2]));\n *\n * //addToMap\n * var addToMap = [polygon1, polygon2, difference];\n */\nfunction difference(\n  features: FeatureCollection<Polygon | MultiPolygon>\n): Feature<Polygon | MultiPolygon> | null {\n  const geoms: Array<polyclip.Geom> = [];\n\n  geomEach(features, (geom) => {\n    geoms.push(geom.coordinates as polyclip.Geom);\n  });\n\n  if (geoms.length < 2) {\n    throw new Error(\"Must have at least two features\");\n  }\n\n  const properties = features.features[0].properties || {};\n\n  const differenced = polyclip.difference(geoms[0], ...geoms.slice(1));\n  if (differenced.length === 0) return null;\n  if (differenced.length === 1) return polygon(differenced[0], properties);\n  return multiPolygon(differenced, properties);\n}\n\nexport { difference };\nexport default difference;\n"], "mappings": ";AACA,YAAY,cAAc;AAC1B,SAAS,SAAS,oBAAoB;AACtC,SAAS,gBAAgB;AAmCzB,SAASA,YACP,UACwC;AACxC,QAAM,QAA8B,CAAC;AAErC,WAAS,UAAU,CAAC,SAAS;AAC3B,UAAM,KAAK,KAAK,WAA4B;AAAA,EAC9C,CAAC;AAED,MAAI,MAAM,SAAS,GAAG;AACpB,UAAM,IAAI,MAAM,iCAAiC;AAAA,EACnD;AAEA,QAAM,aAAa,SAAS,SAAS,CAAC,EAAE,cAAc,CAAC;AAEvD,QAAM,cAAuB,oBAAW,MAAM,CAAC,GAAG,GAAG,MAAM,MAAM,CAAC,CAAC;AACnE,MAAI,YAAY,WAAW,EAAG,QAAO;AACrC,MAAI,YAAY,WAAW,EAAG,QAAO,QAAQ,YAAY,CAAC,GAAG,UAAU;AACvE,SAAO,aAAa,aAAa,UAAU;AAC7C;AAGA,IAAO,0BAAQC;", "names": ["difference", "difference"]}