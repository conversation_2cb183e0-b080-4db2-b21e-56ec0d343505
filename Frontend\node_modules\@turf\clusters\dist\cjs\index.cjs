"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _meta = require('@turf/meta');
var _helpers = require('@turf/helpers');
function getCluster(geojson, filter) {
  if (!geojson) throw new Error("geojson is required");
  if (geojson.type !== "FeatureCollection")
    throw new Error("geojson must be a FeatureCollection");
  if (filter === void 0 || filter === null)
    throw new Error("filter is required");
  var features = [];
  _meta.featureEach.call(void 0, geojson, function(feature) {
    if (applyFilter(feature.properties, filter)) features.push(feature);
  });
  return _helpers.featureCollection.call(void 0, features);
}
function clusterEach(geojson, property, callback) {
  if (!geojson) throw new Error("geojson is required");
  if (geojson.type !== "FeatureCollection")
    throw new Error("geojson must be a FeatureCollection");
  if (property === void 0 || property === null)
    throw new Error("property is required");
  var bins = createBins(geojson, property);
  var values = Object.keys(bins);
  for (var index = 0; index < values.length; index++) {
    var value = values[index];
    var bin = bins[value];
    var features = [];
    for (var i = 0; i < bin.length; i++) {
      features.push(geojson.features[bin[i]]);
    }
    callback(_helpers.featureCollection.call(void 0, features), value, index);
  }
}
function clusterReduce(geojson, property, callback, initialValue) {
  var previousValue = initialValue;
  clusterEach(
    geojson,
    property,
    function(cluster, clusterValue, currentIndex) {
      if (currentIndex === 0 && initialValue === void 0)
        previousValue = cluster;
      else
        previousValue = callback(
          previousValue,
          cluster,
          clusterValue,
          currentIndex
        );
    }
  );
  return previousValue;
}
function createBins(geojson, property) {
  var bins = {};
  _meta.featureEach.call(void 0, geojson, function(feature, i) {
    var properties = feature.properties || {};
    if (Object.prototype.hasOwnProperty.call(properties, String(property))) {
      var value = properties[property];
      if (Object.prototype.hasOwnProperty.call(bins, value))
        bins[value].push(i);
      else bins[value] = [i];
    }
  });
  return bins;
}
function applyFilter(properties, filter) {
  if (properties === void 0) return false;
  var filterType = typeof filter;
  if (filterType === "number" || filterType === "string")
    return Object.prototype.hasOwnProperty.call(properties, filter);
  else if (Array.isArray(filter)) {
    for (var i = 0; i < filter.length; i++) {
      if (!applyFilter(properties, filter[i])) return false;
    }
    return true;
  } else {
    return propertiesContainsFilter(properties, filter);
  }
}
function propertiesContainsFilter(properties, filter) {
  var keys = Object.keys(filter);
  for (var i = 0; i < keys.length; i++) {
    var key = keys[i];
    if (properties[key] !== filter[key]) return false;
  }
  return true;
}
function filterProperties(properties, keys) {
  if (!keys) return {};
  if (!keys.length) return {};
  var newProperties = {};
  for (var i = 0; i < keys.length; i++) {
    var key = keys[i];
    if (Object.prototype.hasOwnProperty.call(properties, key))
      newProperties[key] = properties[key];
  }
  return newProperties;
}








exports.applyFilter = applyFilter; exports.clusterEach = clusterEach; exports.clusterReduce = clusterReduce; exports.createBins = createBins; exports.filterProperties = filterProperties; exports.getCluster = getCluster; exports.propertiesContainsFilter = propertiesContainsFilter;
//# sourceMappingURL=index.cjs.map