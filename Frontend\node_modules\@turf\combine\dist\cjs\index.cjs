"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _helpers = require('@turf/helpers');
var _meta = require('@turf/meta');
function combine(fc) {
  var groups = {
    MultiPoint: {
      coordinates: [],
      properties: []
    },
    MultiLineString: {
      coordinates: [],
      properties: []
    },
    MultiPolygon: {
      coordinates: [],
      properties: []
    }
  };
  _meta.featureEach.call(void 0, fc, (feature2) => {
    var _a;
    switch ((_a = feature2.geometry) == null ? void 0 : _a.type) {
      case "Point":
        groups.MultiPoint.coordinates.push(feature2.geometry.coordinates);
        groups.MultiPoint.properties.push(feature2.properties);
        break;
      case "MultiPoint":
        groups.MultiPoint.coordinates.push(...feature2.geometry.coordinates);
        groups.MultiPoint.properties.push(feature2.properties);
        break;
      case "LineString":
        groups.MultiLineString.coordinates.push(feature2.geometry.coordinates);
        groups.MultiLineString.properties.push(feature2.properties);
        break;
      case "MultiLineString":
        groups.MultiLineString.coordinates.push(
          ...feature2.geometry.coordinates
        );
        groups.MultiLineString.properties.push(feature2.properties);
        break;
      case "Polygon":
        groups.MultiPolygon.coordinates.push(feature2.geometry.coordinates);
        groups.MultiPolygon.properties.push(feature2.properties);
        break;
      case "MultiPolygon":
        groups.MultiPolygon.coordinates.push(...feature2.geometry.coordinates);
        groups.MultiPolygon.properties.push(feature2.properties);
        break;
      default:
        break;
    }
  });
  return _helpers.featureCollection.call(void 0, 
    Object.keys(groups).filter(function(key) {
      return groups[key].coordinates.length;
    }).sort().map(function(key) {
      var geometry = { type: key, coordinates: groups[key].coordinates };
      var properties = { collectedProperties: groups[key].properties };
      return _helpers.feature.call(void 0, geometry, properties);
    })
  );
}
var turf_combine_default = combine;



exports.combine = combine; exports.default = turf_combine_default;
//# sourceMappingURL=index.cjs.map