{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-line-segment/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACSA,wCAA8C;AAC9C,4CAA0B;AAC1B,kCAA4B;AAgB5B,SAAS,WAAA,CAGP,OAAA,EAC+B;AAC/B,EAAA,GAAA,CAAI,CAAC,OAAA,EAAS;AACZ,IAAA,MAAM,IAAI,KAAA,CAAM,qBAAqB,CAAA;AAAA,EACvC;AAEA,EAAA,MAAM,QAAA,EAAsC,CAAC,CAAA;AAC7C,EAAA,+BAAA,OAAY,EAAS,CAAC,OAAA,EAAA,GAA0B;AAC9C,IAAA,kBAAA,CAAmB,OAAA,EAAS,OAAO,CAAA;AAAA,EACrC,CAAC,CAAA;AACD,EAAA,OAAO,wCAAA,OAAyB,CAAA;AAClC;AAUA,SAAS,kBAAA,CACP,OAAA,EACA,OAAA,EACA;AACA,EAAA,IAAI,OAAA,EAAuB,CAAC,CAAA;AAC5B,EAAA,MAAM,SAAA,EAAW,OAAA,CAAQ,QAAA;AACzB,EAAA,GAAA,CAAI,SAAA,IAAa,IAAA,EAAM;AACrB,IAAA,OAAA,CAAQ,QAAA,CAAS,IAAA,EAAM;AAAA,MACrB,KAAK,SAAA;AACH,QAAA,OAAA,EAAS,kCAAA,QAAkB,CAAA;AAC3B,QAAA,KAAA;AAAA,MACF,KAAK,YAAA;AACH,QAAA,OAAA,EAAS,CAAC,kCAAA,QAAkB,CAAC,CAAA;AAAA,IACjC;AACA,IAAA,MAAA,CAAO,OAAA,CAAQ,CAAC,KAAA,EAAA,GAAU;AACxB,MAAA,MAAM,SAAA,EAAW,cAAA,CAAe,KAAA,EAAO,OAAA,CAAQ,UAAU,CAAA;AACzD,MAAA,QAAA,CAAS,OAAA,CAAQ,CAAC,OAAA,EAAA,GAAY;AAC5B,QAAA,OAAA,CAAQ,GAAA,EAAK,OAAA,CAAQ,MAAA;AACrB,QAAA,OAAA,CAAQ,IAAA,CAAK,OAAO,CAAA;AAAA,MACtB,CAAC,CAAA;AAAA,IACH,CAAC,CAAA;AAAA,EACH;AACF;AAUA,SAAS,cAAA,CAAe,MAAA,EAAoB,UAAA,EAAiB;AAC3D,EAAA,MAAM,SAAA,EAAuC,CAAC,CAAA;AAC9C,EAAA,MAAA,CAAO,MAAA,CAAO,CAAC,cAAA,EAAgB,aAAA,EAAA,GAAkB;AAC/C,IAAA,MAAM,QAAA,EAAU,iCAAA,CAAY,cAAA,EAAgB,aAAa,CAAA,EAAG,UAAU,CAAA;AACtE,IAAA,OAAA,CAAQ,KAAA,EAAO,IAAA,CAAK,cAAA,EAAgB,aAAa,CAAA;AACjD,IAAA,QAAA,CAAS,IAAA,CAAK,OAAO,CAAA;AACrB,IAAA,OAAO,aAAA;AAAA,EACT,CAAC,CAAA;AACD,EAAA,OAAO,QAAA;AACT;AAUA,SAAS,IAAA,CAAK,OAAA,EAAmB,OAAA,EAAyB;AACxD,EAAA,MAAM,GAAA,EAAK,OAAA,CAAQ,CAAC,CAAA;AACpB,EAAA,MAAM,GAAA,EAAK,OAAA,CAAQ,CAAC,CAAA;AACpB,EAAA,MAAM,GAAA,EAAK,OAAA,CAAQ,CAAC,CAAA;AACpB,EAAA,MAAM,GAAA,EAAK,OAAA,CAAQ,CAAC,CAAA;AACpB,EAAA,MAAM,KAAA,EAAO,GAAA,EAAK,GAAA,EAAK,GAAA,EAAK,EAAA;AAC5B,EAAA,MAAM,MAAA,EAAQ,GAAA,EAAK,GAAA,EAAK,GAAA,EAAK,EAAA;AAC7B,EAAA,MAAM,KAAA,EAAO,GAAA,EAAK,GAAA,EAAK,GAAA,EAAK,EAAA;AAC5B,EAAA,MAAM,MAAA,EAAQ,GAAA,EAAK,GAAA,EAAK,GAAA,EAAK,EAAA;AAC7B,EAAA,OAAO,CAAC,IAAA,EAAM,KAAA,EAAO,IAAA,EAAM,KAAK,CAAA;AAClC;AAGA,IAAO,0BAAA,EAAQ,WAAA;AD3Df;AACE;AACA;AACF,+EAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-line-segment/dist/cjs/index.cjs", "sourcesContent": [null, "import {\n  BBox,\n  Feature,\n  FeatureCollection,\n  LineString,\n  MultiLineString,\n  MultiPolygon,\n  Polygon,\n} from \"geojson\";\nimport { featureCollection, lineString } from \"@turf/helpers\";\nimport { getCoords } from \"@turf/invariant\";\nimport { flattenEach } from \"@turf/meta\";\n\n/**\n * Creates a {@link FeatureCollection} of 2-vertex {@link LineString} segments from a\n * {@link LineString|(Multi)LineString} or {@link Polygon|(Multi)Polygon}.\n *\n * @function\n * @param {GeoJSON} geojson GeoJSON Polygon or LineString\n * @returns {FeatureCollection<LineString>} 2-vertex line segments\n * @example\n * var polygon = turf.polygon([[[-50, 5], [-40, -10], [-50, -10], [-40, 5], [-50, 5]]]);\n * var segments = turf.lineSegment(polygon);\n *\n * //addToMap\n * var addToMap = [polygon, segments]\n */\nfunction lineSegment<\n  G extends LineString | MultiLineString | Polygon | MultiPolygon,\n>(\n  geojson: Feature<G> | FeatureCollection<G> | G\n): FeatureCollection<LineString> {\n  if (!geojson) {\n    throw new Error(\"geojson is required\");\n  }\n\n  const results: Array<Feature<LineString>> = [];\n  flattenEach(geojson, (feature: Feature<any>) => {\n    lineSegmentFeature(feature, results);\n  });\n  return featureCollection(results);\n}\n\n/**\n * Line Segment\n *\n * @private\n * @param {Feature<LineString|Polygon>} geojson Line or polygon feature\n * @param {Array} results push to results\n * @returns {void}\n */\nfunction lineSegmentFeature(\n  geojson: Feature<LineString | Polygon>,\n  results: Array<Feature<LineString>>\n) {\n  let coords: number[][][] = [];\n  const geometry = geojson.geometry;\n  if (geometry !== null) {\n    switch (geometry.type) {\n      case \"Polygon\":\n        coords = getCoords(geometry);\n        break;\n      case \"LineString\":\n        coords = [getCoords(geometry)];\n    }\n    coords.forEach((coord) => {\n      const segments = createSegments(coord, geojson.properties);\n      segments.forEach((segment) => {\n        segment.id = results.length;\n        results.push(segment);\n      });\n    });\n  }\n}\n\n/**\n * Create Segments from LineString coordinates\n *\n * @private\n * @param {Array<Array<number>>} coords LineString coordinates\n * @param {*} properties GeoJSON properties\n * @returns {Array<Feature<LineString>>} line segments\n */\nfunction createSegments(coords: number[][], properties: any) {\n  const segments: Array<Feature<LineString>> = [];\n  coords.reduce((previousCoords, currentCoords) => {\n    const segment = lineString([previousCoords, currentCoords], properties);\n    segment.bbox = bbox(previousCoords, currentCoords);\n    segments.push(segment);\n    return currentCoords;\n  });\n  return segments;\n}\n\n/**\n * Create BBox between two coordinates (faster than @turf/bbox)\n *\n * @private\n * @param {Array<number>} coords1 Point coordinate\n * @param {Array<number>} coords2 Point coordinate\n * @returns {BBox} [west, south, east, north]\n */\nfunction bbox(coords1: number[], coords2: number[]): BBox {\n  const x1 = coords1[0];\n  const y1 = coords1[1];\n  const x2 = coords2[0];\n  const y2 = coords2[1];\n  const west = x1 < x2 ? x1 : x2;\n  const south = y1 < y2 ? y1 : y2;\n  const east = x1 > x2 ? x1 : x2;\n  const north = y1 > y2 ? y1 : y2;\n  return [west, south, east, north];\n}\n\nexport { lineSegment };\nexport default lineSegment;\n"]}