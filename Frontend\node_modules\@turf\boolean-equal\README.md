# @turf/boolean-equal

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## booleanEqual

Determine whether two geometries of the same type have identical X,Y coordinate values.
See [http://edndoc.esri.com/arcsde/9.0/general\_topics/understand\_spatial\_relations.htm][1]

### Parameters

*   `feature1` **([Geometry][2] | [Feature][3])** GeoJSON input
*   `feature2` **([Geometry][2] | [Feature][3])** GeoJSON input
*   `options` **[Object][4]** Optional parameters (optional, default `{}`)

    *   `options.precision` **[number][5]** decimal precision to use when comparing coordinates (optional, default `6`)

### Examples

```javascript
var pt1 = turf.point([0, 0]);
var pt2 = turf.point([0, 0]);
var pt3 = turf.point([1, 1]);

turf.booleanEqual(pt1, pt2);
//= true
turf.booleanEqual(pt2, pt3);
//= false
```

Returns **[boolean][6]** true if the objects are equal, false otherwise

[1]: http://edndoc.esri.com/arcsde/9.0/general_topics/understand_spatial_relations.htm

[2]: https://tools.ietf.org/html/rfc7946#section-3.1

[3]: https://tools.ietf.org/html/rfc7946#section-3.2

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/boolean-equal
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```


### Diagrams

![esri-equals](diagrams/esri-equals.gif)