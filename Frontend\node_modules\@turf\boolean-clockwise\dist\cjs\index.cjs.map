{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-boolean-clockwise/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,4CAA0B;AAiB1B,SAAS,gBAAA,CACP,IAAA,EACS;AACT,EAAA,MAAM,KAAA,EAAO,kCAAA,IAAc,CAAA;AAC3B,EAAA,IAAI,IAAA,EAAM,CAAA;AACV,EAAA,IAAI,EAAA,EAAI,CAAA;AACR,EAAA,IAAI,IAAA;AACJ,EAAA,IAAI,GAAA;AAEJ,EAAA,MAAA,CAAO,EAAA,EAAI,IAAA,CAAK,MAAA,EAAQ;AACtB,IAAA,KAAA,EAAO,IAAA,GAAO,IAAA,CAAK,CAAC,CAAA;AACpB,IAAA,IAAA,EAAM,IAAA,CAAK,CAAC,CAAA;AACZ,IAAA,IAAA,GAAA,CAAQ,GAAA,CAAI,CAAC,EAAA,EAAI,IAAA,CAAK,CAAC,CAAA,EAAA,EAAA,CAAM,GAAA,CAAI,CAAC,EAAA,EAAI,IAAA,CAAK,CAAC,CAAA,CAAA;AAC5C,IAAA,CAAA,EAAA;AAAA,EACF;AACA,EAAA,OAAO,IAAA,EAAM,CAAA;AACf;AAGA,IAAO,+BAAA,EAAQ,gBAAA;ADpBf;AACE;AACA;AACF,8FAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-boolean-clockwise/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, LineString, Position } from \"geojson\";\nimport { getCoords } from \"@turf/invariant\";\n\n/**\n * Takes a ring and return true or false whether or not the ring is clockwise or counter-clockwise.\n *\n * @function\n * @param {Feature<LineString>|LineString|Array<Array<number>>} line to be evaluated\n * @returns {boolean} true/false\n * @example\n * var clockwiseRing = turf.lineString([[0,0],[1,1],[1,0],[0,0]]);\n * var counterClockwiseRing = turf.lineString([[0,0],[1,0],[1,1],[0,0]]);\n *\n * turf.booleanClockwise(clockwiseRing)\n * //=true\n * turf.booleanClockwise(counterClockwiseRing)\n * //=false\n */\nfunction booleanClockwise(\n  line: Feature<LineString> | LineString | Position[]\n): boolean {\n  const ring = getCoords(line);\n  let sum = 0;\n  let i = 1;\n  let prev;\n  let cur;\n\n  while (i < ring.length) {\n    prev = cur || ring[0];\n    cur = ring[i];\n    sum += (cur[0] - prev[0]) * (cur[1] + prev[1]);\n    i++;\n  }\n  return sum > 0;\n}\n\nexport { booleanClockwise };\nexport default booleanClockwise;\n"]}