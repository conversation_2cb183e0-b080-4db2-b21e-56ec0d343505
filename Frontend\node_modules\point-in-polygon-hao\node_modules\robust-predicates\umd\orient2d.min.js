!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).predicates={})}(this,(function(t){"use strict";const e=11102230246251565e-32,n=134217729,o=(3+8*e)*e;function r(t,e,n,o,r){let f,i,u,s,c=e[0],a=o[0],l=0,d=0;a>c==a>-c?(f=c,c=e[++l]):(f=a,a=o[++d]);let p=0;if(l<t&&d<n)for(a>c==a>-c?(i=c+f,u=f-(i-c),c=e[++l]):(i=a+f,u=f-(i-a),a=o[++d]),f=i,0!==u&&(r[p++]=u);l<t&&d<n;)a>c==a>-c?(i=f+c,s=i-f,u=f-(i-s)+(c-s),c=e[++l]):(i=f+a,s=i-f,u=f-(i-s)+(a-s),a=o[++d]),f=i,0!==u&&(r[p++]=u);for(;l<t;)i=f+c,s=i-f,u=f-(i-s)+(c-s),c=e[++l],f=i,0!==u&&(r[p++]=u);for(;d<n;)i=f+a,s=i-f,u=f-(i-s)+(a-s),a=o[++d],f=i,0!==u&&(r[p++]=u);return 0===f&&0!==p||(r[p++]=f),p}function f(t){return new Float64Array(t)}const i=22204460492503146e-32,u=11093356479670487e-47,s=f(4),c=f(8),a=f(12),l=f(16),d=f(4);t.orient2d=function(t,e,f,p,b,h){const y=(e-h)*(f-b),x=(t-b)*(p-h),M=y-x,g=Math.abs(y+x);return Math.abs(M)>=33306690738754716e-32*g?M:-function(t,e,f,p,b,h,y){let x,M,g,m,T,j,w,A,F,k,q,v,z,B,C,D,E,G;const H=t-b,I=f-b,J=e-h,K=p-h;B=H*K,j=n*H,w=j-(j-H),A=H-w,j=n*K,F=j-(j-K),k=K-F,C=A*k-(B-w*F-A*F-w*k),D=J*I,j=n*J,w=j-(j-J),A=J-w,j=n*I,F=j-(j-I),k=I-F,E=A*k-(D-w*F-A*F-w*k),q=C-E,T=C-q,s[0]=C-(q+T)+(T-E),v=B+q,T=v-B,z=B-(v-T)+(q-T),q=z-D,T=z-q,s[1]=z-(q+T)+(T-D),G=v+q,T=G-v,s[2]=v-(G-T)+(q-T),s[3]=G;let L=function(t,e){let n=e[0];for(let o=1;o<t;o++)n+=e[o];return n}(4,s),N=i*y;if(L>=N||-L>=N)return L;if(T=t-H,x=t-(H+T)+(T-b),T=f-I,g=f-(I+T)+(T-b),T=e-J,M=e-(J+T)+(T-h),T=p-K,m=p-(K+T)+(T-h),0===x&&0===M&&0===g&&0===m)return L;if(N=u*y+o*Math.abs(L),L+=H*m+K*x-(J*g+I*M),L>=N||-L>=N)return L;B=x*K,j=n*x,w=j-(j-x),A=x-w,j=n*K,F=j-(j-K),k=K-F,C=A*k-(B-w*F-A*F-w*k),D=M*I,j=n*M,w=j-(j-M),A=M-w,j=n*I,F=j-(j-I),k=I-F,E=A*k-(D-w*F-A*F-w*k),q=C-E,T=C-q,d[0]=C-(q+T)+(T-E),v=B+q,T=v-B,z=B-(v-T)+(q-T),q=z-D,T=z-q,d[1]=z-(q+T)+(T-D),G=v+q,T=G-v,d[2]=v-(G-T)+(q-T),d[3]=G;const O=r(4,s,4,d,c);B=H*m,j=n*H,w=j-(j-H),A=H-w,j=n*m,F=j-(j-m),k=m-F,C=A*k-(B-w*F-A*F-w*k),D=J*g,j=n*J,w=j-(j-J),A=J-w,j=n*g,F=j-(j-g),k=g-F,E=A*k-(D-w*F-A*F-w*k),q=C-E,T=C-q,d[0]=C-(q+T)+(T-E),v=B+q,T=v-B,z=B-(v-T)+(q-T),q=z-D,T=z-q,d[1]=z-(q+T)+(T-D),G=v+q,T=G-v,d[2]=v-(G-T)+(q-T),d[3]=G;const P=r(O,c,4,d,a);B=x*m,j=n*x,w=j-(j-x),A=x-w,j=n*m,F=j-(j-m),k=m-F,C=A*k-(B-w*F-A*F-w*k),D=M*g,j=n*M,w=j-(j-M),A=M-w,j=n*g,F=j-(j-g),k=g-F,E=A*k-(D-w*F-A*F-w*k),q=C-E,T=C-q,d[0]=C-(q+T)+(T-E),v=B+q,T=v-B,z=B-(v-T)+(q-T),q=z-D,T=z-q,d[1]=z-(q+T)+(T-D),G=v+q,T=G-v,d[2]=v-(G-T)+(q-T),d[3]=G;const Q=r(P,a,4,d,l);return l[Q-1]}(t,e,f,p,b,h,g)},t.orient2dfast=function(t,e,n,o,r,f){return(e-f)*(n-r)-(t-r)*(o-f)}}));
