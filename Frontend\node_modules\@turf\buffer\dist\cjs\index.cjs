"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }// index.js
var _center = require('@turf/center');
var _jsts = require('@turf/jsts'); var _jsts2 = _interopRequireDefault(_jsts);
var _meta = require('@turf/meta');
var _d3geo = require('d3-geo');






var _helpers = require('@turf/helpers');
var { <PERSON><PERSON><PERSON><PERSON>p, GeoJSONReader, GeoJSONWriter } = _jsts2.default;
function buffer(geojson, radius, options) {
  options = options || {};
  var units = options.units || "kilometers";
  var steps = options.steps || 8;
  if (!geojson) throw new Error("geojson is required");
  if (typeof options !== "object") throw new Error("options must be an object");
  if (typeof steps !== "number") throw new Error("steps must be an number");
  if (radius === void 0) throw new Error("radius is required");
  if (steps <= 0) throw new Error("steps must be greater than 0");
  var results = [];
  switch (geojson.type) {
    case "GeometryCollection":
      _meta.geomEach.call(void 0, geojson, function(geometry) {
        var buffered = bufferFeature(geometry, radius, units, steps);
        if (buffered) results.push(buffered);
      });
      return _helpers.featureCollection.call(void 0, results);
    case "FeatureCollection":
      _meta.featureEach.call(void 0, geojson, function(feature2) {
        var multiBuffered = bufferFeature(feature2, radius, units, steps);
        if (multiBuffered) {
          _meta.featureEach.call(void 0, multiBuffered, function(buffered) {
            if (buffered) results.push(buffered);
          });
        }
      });
      return _helpers.featureCollection.call(void 0, results);
  }
  return bufferFeature(geojson, radius, units, steps);
}
function bufferFeature(geojson, radius, units, steps) {
  var properties = geojson.properties || {};
  var geometry = geojson.type === "Feature" ? geojson.geometry : geojson;
  if (geometry.type === "GeometryCollection") {
    var results = [];
    _meta.geomEach.call(void 0, geojson, function(geometry2) {
      var buffered2 = bufferFeature(geometry2, radius, units, steps);
      if (buffered2) results.push(buffered2);
    });
    return _helpers.featureCollection.call(void 0, results);
  }
  var projection = defineProjection(geometry);
  var projected = {
    type: geometry.type,
    coordinates: projectCoords(geometry.coordinates, projection)
  };
  var reader = new GeoJSONReader();
  var geom = reader.read(projected);
  var distance = _helpers.radiansToLength.call(void 0, _helpers.lengthToRadians.call(void 0, radius, units), "meters");
  var buffered = BufferOp.bufferOp(geom, distance, steps);
  var writer = new GeoJSONWriter();
  buffered = writer.write(buffered);
  if (coordsIsNaN(buffered.coordinates)) return void 0;
  var result = {
    type: buffered.type,
    coordinates: unprojectCoords(buffered.coordinates, projection)
  };
  return _helpers.feature.call(void 0, result, properties);
}
function coordsIsNaN(coords) {
  if (Array.isArray(coords[0])) return coordsIsNaN(coords[0]);
  return isNaN(coords[0]);
}
function projectCoords(coords, proj) {
  if (typeof coords[0] !== "object") return proj(coords);
  return coords.map(function(coord) {
    return projectCoords(coord, proj);
  });
}
function unprojectCoords(coords, proj) {
  if (typeof coords[0] !== "object") return proj.invert(coords);
  return coords.map(function(coord) {
    return unprojectCoords(coord, proj);
  });
}
function defineProjection(geojson) {
  var coords = _center.center.call(void 0, geojson).geometry.coordinates;
  var rotation = [-coords[0], -coords[1]];
  return _d3geo.geoAzimuthalEquidistant.call(void 0, ).rotate(rotation).scale(_helpers.earthRadius);
}
var turf_buffer_default = buffer;



exports.buffer = buffer; exports.default = turf_buffer_default;
//# sourceMappingURL=index.cjs.map