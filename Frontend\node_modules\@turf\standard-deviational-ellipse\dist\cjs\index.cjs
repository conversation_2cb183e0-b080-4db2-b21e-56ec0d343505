"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _meta = require('@turf/meta');
var _invariant = require('@turf/invariant');
var _helpers = require('@turf/helpers');
var _centermean = require('@turf/center-mean');
var _pointswithinpolygon = require('@turf/points-within-polygon');
var _ellipse = require('@turf/ellipse');
function standardDeviationalEllipse(points, options) {
  var _a;
  options = options || {};
  if (!_helpers.isObject.call(void 0, options)) throw new Error("options is invalid");
  const steps = options.steps || 64;
  const weightTerm = options.weight;
  const properties = options.properties || {};
  if (!_helpers.isNumber.call(void 0, steps)) throw new Error("steps must be a number");
  if (!_helpers.isObject.call(void 0, properties)) throw new Error("properties must be a number");
  const numberOfFeatures = _meta.coordAll.call(void 0, points).length;
  const meanCenter = _centermean.centerMean.call(void 0, points, { weight: weightTerm });
  let xDeviationSquaredSum = 0;
  let yDeviationSquaredSum = 0;
  let xyDeviationSum = 0;
  _meta.featureEach.call(void 0, points, function(point) {
    var _a2;
    const weight = weightTerm ? ((_a2 = point.properties) == null ? void 0 : _a2[weightTerm]) || 1 : 1;
    const deviation = getDeviations(_invariant.getCoords.call(void 0, point), _invariant.getCoords.call(void 0, meanCenter));
    xDeviationSquaredSum += Math.pow(deviation.x, 2) * weight;
    yDeviationSquaredSum += Math.pow(deviation.y, 2) * weight;
    xyDeviationSum += deviation.x * deviation.y * weight;
  });
  const bigA = xDeviationSquaredSum - yDeviationSquaredSum;
  const bigB = Math.sqrt(Math.pow(bigA, 2) + 4 * Math.pow(xyDeviationSum, 2));
  const bigC = 2 * xyDeviationSum;
  const theta = Math.atan((bigA + bigB) / bigC);
  const thetaDeg = theta * 180 / Math.PI;
  let sigmaXsum = 0;
  let sigmaYsum = 0;
  let weightsum = 0;
  _meta.featureEach.call(void 0, points, function(point) {
    var _a2;
    const weight = weightTerm ? ((_a2 = point.properties) == null ? void 0 : _a2[weightTerm]) || 1 : 1;
    const deviation = getDeviations(_invariant.getCoords.call(void 0, point), _invariant.getCoords.call(void 0, meanCenter));
    sigmaXsum += Math.pow(
      deviation.x * Math.cos(theta) - deviation.y * Math.sin(theta),
      2
    ) * weight;
    sigmaYsum += Math.pow(
      deviation.x * Math.sin(theta) + deviation.y * Math.cos(theta),
      2
    ) * weight;
    weightsum += weight;
  });
  const sigmaX = Math.sqrt(2 * sigmaXsum / weightsum);
  const sigmaY = Math.sqrt(2 * sigmaYsum / weightsum);
  const theEllipse = _ellipse.ellipse.call(void 0, meanCenter, sigmaX, sigmaY, {
    units: "degrees",
    angle: thetaDeg,
    steps,
    properties
  });
  const pointsWithinEllipse = _pointswithinpolygon.pointsWithinPolygon.call(void 0, 
    points,
    _helpers.featureCollection.call(void 0, [theEllipse])
  );
  const standardDeviationalEllipseProperties = {
    meanCenterCoordinates: _invariant.getCoords.call(void 0, meanCenter),
    semiMajorAxis: sigmaX,
    semiMinorAxis: sigmaY,
    numberOfFeatures,
    angle: thetaDeg,
    percentageWithinEllipse: 100 * _meta.coordAll.call(void 0, pointsWithinEllipse).length / numberOfFeatures
  };
  theEllipse.properties = (_a = theEllipse.properties) != null ? _a : {};
  theEllipse.properties.standardDeviationalEllipse = standardDeviationalEllipseProperties;
  return theEllipse;
}
function getDeviations(coordinates, center) {
  return {
    x: coordinates[0] - center[0],
    y: coordinates[1] - center[1]
  };
}
var turf_standard_deviational_ellipse_default = standardDeviationalEllipse;



exports.default = turf_standard_deviational_ellipse_default; exports.standardDeviationalEllipse = standardDeviationalEllipse;
//# sourceMappingURL=index.cjs.map