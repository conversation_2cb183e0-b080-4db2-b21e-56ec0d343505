"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _bbox = require('@turf/bbox');
var _booleanpointonline = require('@turf/boolean-point-on-line');
var _booleanpointinpolygon = require('@turf/boolean-point-in-polygon');
var _invariant = require('@turf/invariant');
function booleanWithin(feature1, feature2) {
  var geom1 = _invariant.getGeom.call(void 0, feature1);
  var geom2 = _invariant.getGeom.call(void 0, feature2);
  var type1 = geom1.type;
  var type2 = geom2.type;
  switch (type1) {
    case "Point":
      switch (type2) {
        case "MultiPoint":
          return isPointInMultiPoint(geom1, geom2);
        case "LineString":
          return _booleanpointonline.booleanPointOnLine.call(void 0, geom1, geom2, { ignoreEndVertices: true });
        case "Polygon":
        case "MultiPolygon":
          return _booleanpointinpolygon.booleanPointInPolygon.call(void 0, geom1, geom2, { ignoreBoundary: true });
        default:
          throw new Error("feature2 " + type2 + " geometry not supported");
      }
    case "MultiPoint":
      switch (type2) {
        case "MultiPoint":
          return isMultiPointInMultiPoint(geom1, geom2);
        case "LineString":
          return isMultiPointOnLine(geom1, geom2);
        case "Polygon":
        case "MultiPolygon":
          return isMultiPointInPoly(geom1, geom2);
        default:
          throw new Error("feature2 " + type2 + " geometry not supported");
      }
    case "LineString":
      switch (type2) {
        case "LineString":
          return isLineOnLine(geom1, geom2);
        case "Polygon":
        case "MultiPolygon":
          return isLineInPoly(geom1, geom2);
        default:
          throw new Error("feature2 " + type2 + " geometry not supported");
      }
    case "Polygon":
      switch (type2) {
        case "Polygon":
        case "MultiPolygon":
          return isPolyInPoly(geom1, geom2);
        default:
          throw new Error("feature2 " + type2 + " geometry not supported");
      }
    default:
      throw new Error("feature1 " + type1 + " geometry not supported");
  }
}
function isPointInMultiPoint(point, multiPoint) {
  var i;
  var output = false;
  for (i = 0; i < multiPoint.coordinates.length; i++) {
    if (compareCoords(multiPoint.coordinates[i], point.coordinates)) {
      output = true;
      break;
    }
  }
  return output;
}
function isMultiPointInMultiPoint(multiPoint1, multiPoint2) {
  for (var i = 0; i < multiPoint1.coordinates.length; i++) {
    var anyMatch = false;
    for (var i2 = 0; i2 < multiPoint2.coordinates.length; i2++) {
      if (compareCoords(multiPoint1.coordinates[i], multiPoint2.coordinates[i2])) {
        anyMatch = true;
      }
    }
    if (!anyMatch) {
      return false;
    }
  }
  return true;
}
function isMultiPointOnLine(multiPoint, lineString) {
  var foundInsidePoint = false;
  for (var i = 0; i < multiPoint.coordinates.length; i++) {
    if (!_booleanpointonline.booleanPointOnLine.call(void 0, multiPoint.coordinates[i], lineString)) {
      return false;
    }
    if (!foundInsidePoint) {
      foundInsidePoint = _booleanpointonline.booleanPointOnLine.call(void 0, 
        multiPoint.coordinates[i],
        lineString,
        { ignoreEndVertices: true }
      );
    }
  }
  return foundInsidePoint;
}
function isMultiPointInPoly(multiPoint, polygon) {
  var output = true;
  var oneInside = false;
  var isInside = false;
  for (var i = 0; i < multiPoint.coordinates.length; i++) {
    isInside = _booleanpointinpolygon.booleanPointInPolygon.call(void 0, multiPoint.coordinates[i], polygon);
    if (!isInside) {
      output = false;
      break;
    }
    if (!oneInside) {
      isInside = _booleanpointinpolygon.booleanPointInPolygon.call(void 0, multiPoint.coordinates[i], polygon, {
        ignoreBoundary: true
      });
    }
  }
  return output && isInside;
}
function isLineOnLine(lineString1, lineString2) {
  for (var i = 0; i < lineString1.coordinates.length; i++) {
    if (!_booleanpointonline.booleanPointOnLine.call(void 0, lineString1.coordinates[i], lineString2)) {
      return false;
    }
  }
  return true;
}
function isLineInPoly(linestring, polygon) {
  var polyBbox = _bbox.bbox.call(void 0, polygon);
  var lineBbox = _bbox.bbox.call(void 0, linestring);
  if (!doBBoxOverlap(polyBbox, lineBbox)) {
    return false;
  }
  var foundInsidePoint = false;
  for (var i = 0; i < linestring.coordinates.length; i++) {
    if (!_booleanpointinpolygon.booleanPointInPolygon.call(void 0, linestring.coordinates[i], polygon)) {
      return false;
    }
    if (!foundInsidePoint) {
      foundInsidePoint = _booleanpointinpolygon.booleanPointInPolygon.call(void 0, 
        linestring.coordinates[i],
        polygon,
        { ignoreBoundary: true }
      );
    }
    if (!foundInsidePoint && i < linestring.coordinates.length - 1) {
      var midpoint = getMidpoint(
        linestring.coordinates[i],
        linestring.coordinates[i + 1]
      );
      foundInsidePoint = _booleanpointinpolygon.booleanPointInPolygon.call(void 0, midpoint, polygon, {
        ignoreBoundary: true
      });
    }
  }
  return foundInsidePoint;
}
function isPolyInPoly(geometry1, geometry2) {
  var poly1Bbox = _bbox.bbox.call(void 0, geometry1);
  var poly2Bbox = _bbox.bbox.call(void 0, geometry2);
  if (!doBBoxOverlap(poly2Bbox, poly1Bbox)) {
    return false;
  }
  for (var i = 0; i < geometry1.coordinates[0].length; i++) {
    if (!_booleanpointinpolygon.booleanPointInPolygon.call(void 0, geometry1.coordinates[0][i], geometry2)) {
      return false;
    }
  }
  return true;
}
function doBBoxOverlap(bbox1, bbox2) {
  if (bbox1[0] > bbox2[0]) return false;
  if (bbox1[2] < bbox2[2]) return false;
  if (bbox1[1] > bbox2[1]) return false;
  if (bbox1[3] < bbox2[3]) return false;
  return true;
}
function compareCoords(pair1, pair2) {
  return pair1[0] === pair2[0] && pair1[1] === pair2[1];
}
function getMidpoint(pair1, pair2) {
  return [(pair1[0] + pair2[0]) / 2, (pair1[1] + pair2[1]) / 2];
}
var turf_boolean_within_default = booleanWithin;



exports.booleanWithin = booleanWithin; exports.default = turf_boolean_within_default;
//# sourceMappingURL=index.cjs.map