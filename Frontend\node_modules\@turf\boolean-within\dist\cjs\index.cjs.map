{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-boolean-within/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACUA,kCAAiC;AACjC,iEAAmC;AACnC,uEAAsC;AACtC,4CAAwB;AAmBxB,SAAS,aAAA,CACP,QAAA,EACA,QAAA,EACS;AACT,EAAA,IAAI,MAAA,EAAQ,gCAAA,QAAgB,CAAA;AAC5B,EAAA,IAAI,MAAA,EAAQ,gCAAA,QAAgB,CAAA;AAC5B,EAAA,IAAI,MAAA,EAAQ,KAAA,CAAM,IAAA;AAClB,EAAA,IAAI,MAAA,EAAQ,KAAA,CAAM,IAAA;AAElB,EAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,IACb,KAAK,OAAA;AACH,MAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,QACb,KAAK,YAAA;AACH,UAAA,OAAO,mBAAA,CAAoB,KAAA,EAAO,KAAK,CAAA;AAAA,QACzC,KAAK,YAAA;AACH,UAAA,OAAO,oDAAA,KAAmB,EAAO,KAAA,EAAO,EAAE,iBAAA,EAAmB,KAAK,CAAC,CAAA;AAAA,QACrE,KAAK,SAAA;AAAA,QACL,KAAK,cAAA;AACH,UAAA,OAAO,0DAAA,KAAsB,EAAO,KAAA,EAAO,EAAE,cAAA,EAAgB,KAAK,CAAC,CAAA;AAAA,QACrE,OAAA;AACE,UAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,MACnE;AAAA,IACF,KAAK,YAAA;AACH,MAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,QACb,KAAK,YAAA;AACH,UAAA,OAAO,wBAAA,CAAyB,KAAA,EAAO,KAAK,CAAA;AAAA,QAC9C,KAAK,YAAA;AACH,UAAA,OAAO,kBAAA,CAAmB,KAAA,EAAO,KAAK,CAAA;AAAA,QACxC,KAAK,SAAA;AAAA,QACL,KAAK,cAAA;AACH,UAAA,OAAO,kBAAA,CAAmB,KAAA,EAAO,KAAK,CAAA;AAAA,QACxC,OAAA;AACE,UAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,MACnE;AAAA,IACF,KAAK,YAAA;AACH,MAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,QACb,KAAK,YAAA;AACH,UAAA,OAAO,YAAA,CAAa,KAAA,EAAO,KAAK,CAAA;AAAA,QAClC,KAAK,SAAA;AAAA,QACL,KAAK,cAAA;AACH,UAAA,OAAO,YAAA,CAAa,KAAA,EAAO,KAAK,CAAA;AAAA,QAClC,OAAA;AACE,UAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,MACnE;AAAA,IACF,KAAK,SAAA;AACH,MAAA,OAAA,CAAQ,KAAA,EAAO;AAAA,QACb,KAAK,SAAA;AAAA,QACL,KAAK,cAAA;AACH,UAAA,OAAO,YAAA,CAAa,KAAA,EAAO,KAAK,CAAA;AAAA,QAClC,OAAA;AACE,UAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,MACnE;AAAA,IACF,OAAA;AACE,MAAA,MAAM,IAAI,KAAA,CAAM,YAAA,EAAc,MAAA,EAAQ,yBAAyB,CAAA;AAAA,EACnE;AACF;AAEA,SAAS,mBAAA,CAAoB,KAAA,EAAc,UAAA,EAAwB;AACjE,EAAA,IAAI,CAAA;AACJ,EAAA,IAAI,OAAA,EAAS,KAAA;AACb,EAAA,IAAA,CAAK,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,UAAA,CAAW,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AAClD,IAAA,GAAA,CAAI,aAAA,CAAc,UAAA,CAAW,WAAA,CAAY,CAAC,CAAA,EAAG,KAAA,CAAM,WAAW,CAAA,EAAG;AAC/D,MAAA,OAAA,EAAS,IAAA;AACT,MAAA,KAAA;AAAA,IACF;AAAA,EACF;AACA,EAAA,OAAO,MAAA;AACT;AAEA,SAAS,wBAAA,CACP,WAAA,EACA,WAAA,EACA;AACA,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,WAAA,CAAY,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACvD,IAAA,IAAI,SAAA,EAAW,KAAA;AACf,IAAA,IAAA,CAAA,IAAS,GAAA,EAAK,CAAA,EAAG,GAAA,EAAK,WAAA,CAAY,WAAA,CAAY,MAAA,EAAQ,EAAA,EAAA,EAAM;AAC1D,MAAA,GAAA,CACE,aAAA,CAAc,WAAA,CAAY,WAAA,CAAY,CAAC,CAAA,EAAG,WAAA,CAAY,WAAA,CAAY,EAAE,CAAC,CAAA,EACrE;AACA,QAAA,SAAA,EAAW,IAAA;AAAA,MACb;AAAA,IACF;AACA,IAAA,GAAA,CAAI,CAAC,QAAA,EAAU;AACb,MAAA,OAAO,KAAA;AAAA,IACT;AAAA,EACF;AACA,EAAA,OAAO,IAAA;AACT;AAEA,SAAS,kBAAA,CAAmB,UAAA,EAAwB,UAAA,EAAwB;AAC1E,EAAA,IAAI,iBAAA,EAAmB,KAAA;AAEvB,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,UAAA,CAAW,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACtD,IAAA,GAAA,CAAI,CAAC,oDAAA,UAAmB,CAAW,WAAA,CAAY,CAAC,CAAA,EAAG,UAAU,CAAA,EAAG;AAC9D,MAAA,OAAO,KAAA;AAAA,IACT;AACA,IAAA,GAAA,CAAI,CAAC,gBAAA,EAAkB;AACrB,MAAA,iBAAA,EAAmB,oDAAA;AAAA,QACjB,UAAA,CAAW,WAAA,CAAY,CAAC,CAAA;AAAA,QACxB,UAAA;AAAA,QACA,EAAE,iBAAA,EAAmB,KAAK;AAAA,MAC5B,CAAA;AAAA,IACF;AAAA,EACF;AACA,EAAA,OAAO,gBAAA;AACT;AAEA,SAAS,kBAAA,CAAmB,UAAA,EAAwB,OAAA,EAAkB;AACpE,EAAA,IAAI,OAAA,EAAS,IAAA;AACb,EAAA,IAAI,UAAA,EAAY,KAAA;AAChB,EAAA,IAAI,SAAA,EAAW,KAAA;AACf,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,UAAA,CAAW,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACtD,IAAA,SAAA,EAAW,0DAAA,UAAsB,CAAW,WAAA,CAAY,CAAC,CAAA,EAAG,OAAO,CAAA;AACnE,IAAA,GAAA,CAAI,CAAC,QAAA,EAAU;AACb,MAAA,OAAA,EAAS,KAAA;AACT,MAAA,KAAA;AAAA,IACF;AACA,IAAA,GAAA,CAAI,CAAC,SAAA,EAAW;AACd,MAAA,SAAA,EAAW,0DAAA,UAAsB,CAAW,WAAA,CAAY,CAAC,CAAA,EAAG,OAAA,EAAS;AAAA,QACnE,cAAA,EAAgB;AAAA,MAClB,CAAC,CAAA;AAAA,IACH;AAAA,EACF;AACA,EAAA,OAAO,OAAA,GAAU,QAAA;AACnB;AAEA,SAAS,YAAA,CAAa,WAAA,EAAyB,WAAA,EAAyB;AACtE,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,WAAA,CAAY,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACvD,IAAA,GAAA,CAAI,CAAC,oDAAA,WAAmB,CAAY,WAAA,CAAY,CAAC,CAAA,EAAG,WAAW,CAAA,EAAG;AAChE,MAAA,OAAO,KAAA;AAAA,IACT;AAAA,EACF;AACA,EAAA,OAAO,IAAA;AACT;AAEA,SAAS,YAAA,CAAa,UAAA,EAAwB,OAAA,EAAkB;AAC9D,EAAA,IAAI,SAAA,EAAW,wBAAA,OAAgB,CAAA;AAC/B,EAAA,IAAI,SAAA,EAAW,wBAAA,UAAmB,CAAA;AAClC,EAAA,GAAA,CAAI,CAAC,aAAA,CAAc,QAAA,EAAU,QAAQ,CAAA,EAAG;AACtC,IAAA,OAAO,KAAA;AAAA,EACT;AACA,EAAA,IAAI,iBAAA,EAAmB,KAAA;AAEvB,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,UAAA,CAAW,WAAA,CAAY,MAAA,EAAQ,CAAA,EAAA,EAAK;AACtD,IAAA,GAAA,CAAI,CAAC,0DAAA,UAAsB,CAAW,WAAA,CAAY,CAAC,CAAA,EAAG,OAAO,CAAA,EAAG;AAC9D,MAAA,OAAO,KAAA;AAAA,IACT;AACA,IAAA,GAAA,CAAI,CAAC,gBAAA,EAAkB;AACrB,MAAA,iBAAA,EAAmB,0DAAA;AAAA,QACjB,UAAA,CAAW,WAAA,CAAY,CAAC,CAAA;AAAA,QACxB,OAAA;AAAA,QACA,EAAE,cAAA,EAAgB,KAAK;AAAA,MACzB,CAAA;AAAA,IACF;AACA,IAAA,GAAA,CAAI,CAAC,iBAAA,GAAoB,EAAA,EAAI,UAAA,CAAW,WAAA,CAAY,OAAA,EAAS,CAAA,EAAG;AAC9D,MAAA,IAAI,SAAA,EAAW,WAAA;AAAA,QACb,UAAA,CAAW,WAAA,CAAY,CAAC,CAAA;AAAA,QACxB,UAAA,CAAW,WAAA,CAAY,EAAA,EAAI,CAAC;AAAA,MAC9B,CAAA;AACA,MAAA,iBAAA,EAAmB,0DAAA,QAAsB,EAAU,OAAA,EAAS;AAAA,QAC1D,cAAA,EAAgB;AAAA,MAClB,CAAC,CAAA;AAAA,IACH;AAAA,EACF;AACA,EAAA,OAAO,gBAAA;AACT;AAWA,SAAS,YAAA,CAAa,SAAA,EAAoB,SAAA,EAAmC;AAC3E,EAAA,IAAI,UAAA,EAAY,wBAAA,SAAkB,CAAA;AAClC,EAAA,IAAI,UAAA,EAAY,wBAAA,SAAkB,CAAA;AAClC,EAAA,GAAA,CAAI,CAAC,aAAA,CAAc,SAAA,EAAW,SAAS,CAAA,EAAG;AACxC,IAAA,OAAO,KAAA;AAAA,EACT;AACA,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,SAAA,CAAU,WAAA,CAAY,CAAC,CAAA,CAAE,MAAA,EAAQ,CAAA,EAAA,EAAK;AACxD,IAAA,GAAA,CAAI,CAAC,0DAAA,SAAsB,CAAU,WAAA,CAAY,CAAC,CAAA,CAAE,CAAC,CAAA,EAAG,SAAS,CAAA,EAAG;AAClE,MAAA,OAAO,KAAA;AAAA,IACT;AAAA,EACF;AACA,EAAA,OAAO,IAAA;AACT;AAEA,SAAS,aAAA,CAAc,KAAA,EAAa,KAAA,EAAa;AAC/C,EAAA,GAAA,CAAI,KAAA,CAAM,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA,EAAG,OAAO,KAAA;AAChC,EAAA,GAAA,CAAI,KAAA,CAAM,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA,EAAG,OAAO,KAAA;AAChC,EAAA,GAAA,CAAI,KAAA,CAAM,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA,EAAG,OAAO,KAAA;AAChC,EAAA,GAAA,CAAI,KAAA,CAAM,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA,EAAG,OAAO,KAAA;AAChC,EAAA,OAAO,IAAA;AACT;AAUA,SAAS,aAAA,CAAc,KAAA,EAAiB,KAAA,EAAiB;AACvD,EAAA,OAAO,KAAA,CAAM,CAAC,EAAA,IAAM,KAAA,CAAM,CAAC,EAAA,GAAK,KAAA,CAAM,CAAC,EAAA,IAAM,KAAA,CAAM,CAAC,CAAA;AACtD;AAUA,SAAS,WAAA,CAAY,KAAA,EAAiB,KAAA,EAAiB;AACrD,EAAA,OAAO,CAAA,CAAE,KAAA,CAAM,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA,EAAA,EAAK,CAAA,EAAA,CAAI,KAAA,CAAM,CAAC,EAAA,EAAI,KAAA,CAAM,CAAC,CAAA,EAAA,EAAK,CAAC,CAAA;AAC9D;AAGA,IAAO,4BAAA,EAAQ,aAAA;AD1Ef;AACE;AACA;AACF,qFAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-boolean-within/dist/cjs/index.cjs", "sourcesContent": [null, "import {\n  BBox,\n  Feature,\n  Geometry,\n  LineString,\n  MultiPoint,\n  MultiPolygon,\n  Point,\n  Polygon,\n} from \"geojson\";\nimport { bbox as calcBbox } from \"@turf/bbox\";\nimport { booleanPointOnLine } from \"@turf/boolean-point-on-line\";\nimport { booleanPointInPolygon } from \"@turf/boolean-point-in-polygon\";\nimport { getGeom } from \"@turf/invariant\";\n\n/**\n * Boolean-within returns true if the first geometry is completely within the second geometry.\n * The interiors of both geometries must intersect and, the interior and boundary of the primary (geometry a)\n * must not intersect the exterior of the secondary (geometry b).\n * Boolean-within returns the exact opposite result of the `@turf/boolean-contains`.\n *\n * @function\n * @param {Geometry|Feature<any>} feature1 GeoJSON Feature or Geometry\n * @param {Geometry|Feature<any>} feature2 GeoJSON Feature or Geometry\n * @returns {boolean} true/false\n * @example\n * var line = turf.lineString([[1, 1], [1, 2], [1, 3], [1, 4]]);\n * var point = turf.point([1, 2]);\n *\n * turf.booleanWithin(point, line);\n * //=true\n */\nfunction booleanWithin(\n  feature1: Feature<any> | Geometry,\n  feature2: Feature<any> | Geometry\n): boolean {\n  var geom1 = getGeom(feature1);\n  var geom2 = getGeom(feature2);\n  var type1 = geom1.type;\n  var type2 = geom2.type;\n\n  switch (type1) {\n    case \"Point\":\n      switch (type2) {\n        case \"MultiPoint\":\n          return isPointInMultiPoint(geom1, geom2);\n        case \"LineString\":\n          return booleanPointOnLine(geom1, geom2, { ignoreEndVertices: true });\n        case \"Polygon\":\n        case \"MultiPolygon\":\n          return booleanPointInPolygon(geom1, geom2, { ignoreBoundary: true });\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"MultiPoint\":\n      switch (type2) {\n        case \"MultiPoint\":\n          return isMultiPointInMultiPoint(geom1, geom2);\n        case \"LineString\":\n          return isMultiPointOnLine(geom1, geom2);\n        case \"Polygon\":\n        case \"MultiPolygon\":\n          return isMultiPointInPoly(geom1, geom2);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"LineString\":\n      switch (type2) {\n        case \"LineString\":\n          return isLineOnLine(geom1, geom2);\n        case \"Polygon\":\n        case \"MultiPolygon\":\n          return isLineInPoly(geom1, geom2);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    case \"Polygon\":\n      switch (type2) {\n        case \"Polygon\":\n        case \"MultiPolygon\":\n          return isPolyInPoly(geom1, geom2);\n        default:\n          throw new Error(\"feature2 \" + type2 + \" geometry not supported\");\n      }\n    default:\n      throw new Error(\"feature1 \" + type1 + \" geometry not supported\");\n  }\n}\n\nfunction isPointInMultiPoint(point: Point, multiPoint: MultiPoint) {\n  var i;\n  var output = false;\n  for (i = 0; i < multiPoint.coordinates.length; i++) {\n    if (compareCoords(multiPoint.coordinates[i], point.coordinates)) {\n      output = true;\n      break;\n    }\n  }\n  return output;\n}\n\nfunction isMultiPointInMultiPoint(\n  multiPoint1: MultiPoint,\n  multiPoint2: MultiPoint\n) {\n  for (var i = 0; i < multiPoint1.coordinates.length; i++) {\n    var anyMatch = false;\n    for (var i2 = 0; i2 < multiPoint2.coordinates.length; i2++) {\n      if (\n        compareCoords(multiPoint1.coordinates[i], multiPoint2.coordinates[i2])\n      ) {\n        anyMatch = true;\n      }\n    }\n    if (!anyMatch) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction isMultiPointOnLine(multiPoint: MultiPoint, lineString: LineString) {\n  var foundInsidePoint = false;\n\n  for (var i = 0; i < multiPoint.coordinates.length; i++) {\n    if (!booleanPointOnLine(multiPoint.coordinates[i], lineString)) {\n      return false;\n    }\n    if (!foundInsidePoint) {\n      foundInsidePoint = booleanPointOnLine(\n        multiPoint.coordinates[i],\n        lineString,\n        { ignoreEndVertices: true }\n      );\n    }\n  }\n  return foundInsidePoint;\n}\n\nfunction isMultiPointInPoly(multiPoint: MultiPoint, polygon: Polygon) {\n  var output = true;\n  var oneInside = false;\n  var isInside = false;\n  for (var i = 0; i < multiPoint.coordinates.length; i++) {\n    isInside = booleanPointInPolygon(multiPoint.coordinates[i], polygon);\n    if (!isInside) {\n      output = false;\n      break;\n    }\n    if (!oneInside) {\n      isInside = booleanPointInPolygon(multiPoint.coordinates[i], polygon, {\n        ignoreBoundary: true,\n      });\n    }\n  }\n  return output && isInside;\n}\n\nfunction isLineOnLine(lineString1: LineString, lineString2: LineString) {\n  for (var i = 0; i < lineString1.coordinates.length; i++) {\n    if (!booleanPointOnLine(lineString1.coordinates[i], lineString2)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction isLineInPoly(linestring: LineString, polygon: Polygon) {\n  var polyBbox = calcBbox(polygon);\n  var lineBbox = calcBbox(linestring);\n  if (!doBBoxOverlap(polyBbox, lineBbox)) {\n    return false;\n  }\n  var foundInsidePoint = false;\n\n  for (var i = 0; i < linestring.coordinates.length; i++) {\n    if (!booleanPointInPolygon(linestring.coordinates[i], polygon)) {\n      return false;\n    }\n    if (!foundInsidePoint) {\n      foundInsidePoint = booleanPointInPolygon(\n        linestring.coordinates[i],\n        polygon,\n        { ignoreBoundary: true }\n      );\n    }\n    if (!foundInsidePoint && i < linestring.coordinates.length - 1) {\n      var midpoint = getMidpoint(\n        linestring.coordinates[i],\n        linestring.coordinates[i + 1]\n      );\n      foundInsidePoint = booleanPointInPolygon(midpoint, polygon, {\n        ignoreBoundary: true,\n      });\n    }\n  }\n  return foundInsidePoint;\n}\n\n/**\n * Is Polygon2 in Polygon1\n * Only takes into account outer rings\n *\n * @private\n * @param {Polygon} geometry1\n * @param {Polygon|MultiPolygon} geometry2\n * @returns {boolean} true/false\n */\nfunction isPolyInPoly(geometry1: Polygon, geometry2: Polygon | MultiPolygon) {\n  var poly1Bbox = calcBbox(geometry1);\n  var poly2Bbox = calcBbox(geometry2);\n  if (!doBBoxOverlap(poly2Bbox, poly1Bbox)) {\n    return false;\n  }\n  for (var i = 0; i < geometry1.coordinates[0].length; i++) {\n    if (!booleanPointInPolygon(geometry1.coordinates[0][i], geometry2)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction doBBoxOverlap(bbox1: BBox, bbox2: BBox) {\n  if (bbox1[0] > bbox2[0]) return false;\n  if (bbox1[2] < bbox2[2]) return false;\n  if (bbox1[1] > bbox2[1]) return false;\n  if (bbox1[3] < bbox2[3]) return false;\n  return true;\n}\n\n/**\n * compareCoords\n *\n * @private\n * @param {Position} pair1 point [x,y]\n * @param {Position} pair2 point [x,y]\n * @returns {boolean} true/false if coord pairs match\n */\nfunction compareCoords(pair1: number[], pair2: number[]) {\n  return pair1[0] === pair2[0] && pair1[1] === pair2[1];\n}\n\n/**\n * getMidpoint\n *\n * @private\n * @param {Position} pair1 point [x,y]\n * @param {Position} pair2 point [x,y]\n * @returns {Position} midpoint of pair1 and pair2\n */\nfunction getMidpoint(pair1: number[], pair2: number[]) {\n  return [(pair1[0] + pair2[0]) / 2, (pair1[1] + pair2[1]) / 2];\n}\n\nexport { booleanWithin };\nexport default booleanWithin;\n"]}