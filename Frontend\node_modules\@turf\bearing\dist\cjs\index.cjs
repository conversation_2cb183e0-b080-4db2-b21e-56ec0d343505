"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _helpers = require('@turf/helpers');
var _invariant = require('@turf/invariant');
function bearing(start, end, options = {}) {
  if (options.final === true) {
    return calculateFinalBearing(start, end);
  }
  const coordinates1 = _invariant.getCoord.call(void 0, start);
  const coordinates2 = _invariant.getCoord.call(void 0, end);
  const lon1 = _helpers.degreesToRadians.call(void 0, coordinates1[0]);
  const lon2 = _helpers.degreesToRadians.call(void 0, coordinates2[0]);
  const lat1 = _helpers.degreesToRadians.call(void 0, coordinates1[1]);
  const lat2 = _helpers.degreesToRadians.call(void 0, coordinates2[1]);
  const a = Math.sin(lon2 - lon1) * Math.cos(lat2);
  const b = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(lon2 - lon1);
  return _helpers.radiansToDegrees.call(void 0, Math.atan2(a, b));
}
function calculateFinalBearing(start, end) {
  let bear = bearing(end, start);
  bear = (bear + 180) % 360;
  return bear;
}
var turf_bearing_default = bearing;



exports.bearing = bearing; exports.default = turf_bearing_default;
//# sourceMappingURL=index.cjs.map