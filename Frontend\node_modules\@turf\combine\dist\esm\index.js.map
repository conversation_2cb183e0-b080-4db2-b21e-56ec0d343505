{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import {\n  GeoJsonProperties,\n  FeatureCollection,\n  LineString,\n  MultiLineString,\n  MultiPoint,\n  MultiPolygon,\n  Point,\n  Polygon,\n} from \"geojson\";\nimport { feature, featureCollection } from \"@turf/helpers\";\nimport { featureEach } from \"@turf/meta\";\n\n/**\n * Combines a {@link FeatureCollection} of {@link Point}, {@link LineString}, or {@link Polygon} features\n * into {@link MultiPoint}, {@link MultiLineString}, or {@link MultiPolygon} features.\n *\n * @function\n * @param {FeatureCollection<Point|LineString|Polygon>} fc a FeatureCollection of any type\n * @returns {FeatureCollection<MultiPoint|MultiLineString|MultiPolygon>} a FeatureCollection of corresponding type to input\n * @example\n * var fc = turf.featureCollection([\n *   turf.point([19.026432, 47.49134]),\n *   turf.point([19.074497, 47.509548])\n * ]);\n *\n * var combined = turf.combine(fc);\n *\n * //addToMap\n * var addToMap = [combined]\n */\nfunction combine(\n  fc: FeatureCollection<\n    Point | MultiPoint | LineString | MultiLineString | Polygon | MultiPolygon\n  >\n) {\n  var groups = {\n    MultiPoint: {\n      coordinates: [] as number[][],\n      properties: [] as GeoJsonProperties[],\n    },\n    MultiLineString: {\n      coordinates: [] as number[][][],\n      properties: [] as GeoJsonProperties[],\n    },\n    MultiPolygon: {\n      coordinates: [] as number[][][][],\n      properties: [] as GeoJsonProperties[],\n    },\n  };\n\n  featureEach(fc, (feature) => {\n    switch (feature.geometry?.type) {\n      case \"Point\":\n        groups.MultiPoint.coordinates.push(feature.geometry.coordinates);\n        groups.MultiPoint.properties.push(feature.properties);\n        break;\n      case \"MultiPoint\":\n        groups.MultiPoint.coordinates.push(...feature.geometry.coordinates);\n        groups.MultiPoint.properties.push(feature.properties);\n        break;\n      case \"LineString\":\n        groups.MultiLineString.coordinates.push(feature.geometry.coordinates);\n        groups.MultiLineString.properties.push(feature.properties);\n        break;\n      case \"MultiLineString\":\n        groups.MultiLineString.coordinates.push(\n          ...feature.geometry.coordinates\n        );\n        groups.MultiLineString.properties.push(feature.properties);\n        break;\n      case \"Polygon\":\n        groups.MultiPolygon.coordinates.push(feature.geometry.coordinates);\n        groups.MultiPolygon.properties.push(feature.properties);\n        break;\n      case \"MultiPolygon\":\n        groups.MultiPolygon.coordinates.push(...feature.geometry.coordinates);\n        groups.MultiPolygon.properties.push(feature.properties);\n        break;\n      default:\n        break;\n    }\n  });\n\n  return featureCollection(\n    (Object.keys(groups) as (keyof typeof groups)[])\n      .filter(function (key) {\n        return groups[key].coordinates.length;\n      })\n      .sort()\n      .map(function (key) {\n        var geometry = { type: key, coordinates: groups[key].coordinates } as\n          | MultiPoint\n          | MultiLineString\n          | MultiPolygon;\n        var properties = { collectedProperties: groups[key].properties };\n        return feature(geometry, properties);\n      })\n  );\n}\n\nexport { combine };\nexport default combine;\n"], "mappings": ";AAUA,SAAS,SAAS,yBAAyB;AAC3C,SAAS,mBAAmB;AAoB5B,SAAS,QACP,IAGA;AACA,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,MACV,aAAa,CAAC;AAAA,MACd,YAAY,CAAC;AAAA,IACf;AAAA,IACA,iBAAiB;AAAA,MACf,aAAa,CAAC;AAAA,MACd,YAAY,CAAC;AAAA,IACf;AAAA,IACA,cAAc;AAAA,MACZ,aAAa,CAAC;AAAA,MACd,YAAY,CAAC;AAAA,IACf;AAAA,EACF;AAEA,cAAY,IAAI,CAACA,aAAY;AAnD/B;AAoDI,aAAQ,KAAAA,SAAQ,aAAR,mBAAkB,MAAM;AAAA,MAC9B,KAAK;AACH,eAAO,WAAW,YAAY,KAAKA,SAAQ,SAAS,WAAW;AAC/D,eAAO,WAAW,WAAW,KAAKA,SAAQ,UAAU;AACpD;AAAA,MACF,KAAK;AACH,eAAO,WAAW,YAAY,KAAK,GAAGA,SAAQ,SAAS,WAAW;AAClE,eAAO,WAAW,WAAW,KAAKA,SAAQ,UAAU;AACpD;AAAA,MACF,KAAK;AACH,eAAO,gBAAgB,YAAY,KAAKA,SAAQ,SAAS,WAAW;AACpE,eAAO,gBAAgB,WAAW,KAAKA,SAAQ,UAAU;AACzD;AAAA,MACF,KAAK;AACH,eAAO,gBAAgB,YAAY;AAAA,UACjC,GAAGA,SAAQ,SAAS;AAAA,QACtB;AACA,eAAO,gBAAgB,WAAW,KAAKA,SAAQ,UAAU;AACzD;AAAA,MACF,KAAK;AACH,eAAO,aAAa,YAAY,KAAKA,SAAQ,SAAS,WAAW;AACjE,eAAO,aAAa,WAAW,KAAKA,SAAQ,UAAU;AACtD;AAAA,MACF,KAAK;AACH,eAAO,aAAa,YAAY,KAAK,GAAGA,SAAQ,SAAS,WAAW;AACpE,eAAO,aAAa,WAAW,KAAKA,SAAQ,UAAU;AACtD;AAAA,MACF;AACE;AAAA,IACJ;AAAA,EACF,CAAC;AAED,SAAO;AAAA,IACJ,OAAO,KAAK,MAAM,EAChB,OAAO,SAAU,KAAK;AACrB,aAAO,OAAO,GAAG,EAAE,YAAY;AAAA,IACjC,CAAC,EACA,KAAK,EACL,IAAI,SAAU,KAAK;AAClB,UAAI,WAAW,EAAE,MAAM,KAAK,aAAa,OAAO,GAAG,EAAE,YAAY;AAIjE,UAAI,aAAa,EAAE,qBAAqB,OAAO,GAAG,EAAE,WAAW;AAC/D,aAAO,QAAQ,UAAU,UAAU;AAAA,IACrC,CAAC;AAAA,EACL;AACF;AAGA,IAAO,uBAAQ;", "names": ["feature"]}