# @turf/bbox-polygon

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## bboxPolygon

Takes a bbox and returns an equivalent [polygon][1].

### Parameters

*   `bbox` **[BBox][2]** extent in \[minX, minY, maxX, maxY] order
*   `options` **[Object][3]** Optional parameters (optional, default `{}`)

    *   `options.properties` **[GeoJsonProperties][4]** Translate properties to Polygon (optional, default `{}`)
    *   `options.id` **([string][5] | [number][6])** Translate Id to Polygon (optional, default `{}`)

### Examples

```javascript
var bbox = [0, 0, 10, 10];

var poly = turf.bboxPolygon(bbox);

//addToMap
var addToMap = [poly]
```

Returns **[Feature][4]<[Polygon][1]>** a Polygon representation of the bounding box

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[2]: https://tools.ietf.org/html/rfc7946#section-5

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[4]: https://tools.ietf.org/html/rfc7946#section-3.2

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/bbox-polygon
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
