{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import {\n  Feature,\n  Geometry,\n  LineString,\n  Point,\n  Polygon,\n  Position,\n} from \"geojson\";\nimport { booleanPointInPolygon } from \"@turf/boolean-point-in-polygon\";\nimport { lineIntersect } from \"@turf/line-intersect\";\nimport { flattenEach } from \"@turf/meta\";\nimport { polygonToLine } from \"@turf/polygon-to-line\";\n\n/**\n * Boolean-disjoint returns (TRUE) if the intersection of the two geometries is an empty set.\n *\n * @function\n * @param {Geometry|Feature<any>} feature1 GeoJSON Feature or Geometry\n * @param {Geometry|Feature<any>} feature2 GeoJSON Feature or Geometry\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.ignoreSelfIntersections=true] ignore self-intersections on input features\n * @returns {boolean} true if the intersection is an empty set, false otherwise\n * @example\n * var point = turf.point([2, 2]);\n * var line = turf.lineString([[1, 1], [1, 2], [1, 3], [1, 4]]);\n *\n * turf.booleanDisjoint(line, point);\n * //=true\n */\nfunction booleanDisjoint(\n  feature1: Feature<any> | Geometry,\n  feature2: Feature<any> | Geometry,\n  {\n    ignoreSelfIntersections = true,\n  }: {\n    ignoreSelfIntersections?: boolean;\n  } = { ignoreSelfIntersections: true }\n): boolean {\n  let bool = true;\n  flattenEach(feature1, (flatten1) => {\n    flattenEach(feature2, (flatten2) => {\n      if (bool === false) {\n        return false;\n      }\n      bool = disjoint(\n        flatten1.geometry,\n        flatten2.geometry,\n        ignoreSelfIntersections\n      );\n    });\n  });\n  return bool;\n}\n\n/**\n * Disjoint operation for simple Geometries (Point/LineString/Polygon)\n *\n * @private\n * @param {Geometry<any>} geom1 GeoJSON Geometry\n * @param {Geometry<any>} geom2 GeoJSON Geometry\n * @param {boolean} ignoreSelfIntersections ignore self-intersections on input features\n * @returns {boolean} true if disjoint, false otherwise\n */\nfunction disjoint(geom1: any, geom2: any, ignoreSelfIntersections: boolean) {\n  switch (geom1.type) {\n    case \"Point\":\n      switch (geom2.type) {\n        case \"Point\":\n          return !compareCoords(geom1.coordinates, geom2.coordinates);\n        case \"LineString\":\n          return !isPointOnLine(geom2, geom1);\n        case \"Polygon\":\n          return !booleanPointInPolygon(geom1, geom2);\n      }\n      /* istanbul ignore next */\n      break;\n    case \"LineString\":\n      switch (geom2.type) {\n        case \"Point\":\n          return !isPointOnLine(geom1, geom2);\n        case \"LineString\":\n          return !isLineOnLine(geom1, geom2, ignoreSelfIntersections);\n        case \"Polygon\":\n          return !isLineInPoly(geom2, geom1, ignoreSelfIntersections);\n      }\n      /* istanbul ignore next */\n      break;\n    case \"Polygon\":\n      switch (geom2.type) {\n        case \"Point\":\n          return !booleanPointInPolygon(geom2, geom1);\n        case \"LineString\":\n          return !isLineInPoly(geom1, geom2, ignoreSelfIntersections);\n        case \"Polygon\":\n          return !isPolyInPoly(geom2, geom1, ignoreSelfIntersections);\n      }\n  }\n  return false;\n}\n\n// http://stackoverflow.com/a/11908158/1979085\nfunction isPointOnLine(lineString: LineString, pt: Point) {\n  for (let i = 0; i < lineString.coordinates.length - 1; i++) {\n    if (\n      isPointOnLineSegment(\n        lineString.coordinates[i],\n        lineString.coordinates[i + 1],\n        pt.coordinates\n      )\n    ) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction isLineOnLine(\n  lineString1: LineString,\n  lineString2: LineString,\n  ignoreSelfIntersections: boolean\n) {\n  const doLinesIntersect = lineIntersect(lineString1, lineString2, {\n    ignoreSelfIntersections,\n  });\n  if (doLinesIntersect.features.length > 0) {\n    return true;\n  }\n  return false;\n}\n\nfunction isLineInPoly(\n  polygon: Polygon,\n  lineString: LineString,\n  ignoreSelfIntersections: boolean\n) {\n  for (const coord of lineString.coordinates) {\n    if (booleanPointInPolygon(coord, polygon)) {\n      return true;\n    }\n  }\n  const doLinesIntersect = lineIntersect(lineString, polygonToLine(polygon), {\n    ignoreSelfIntersections,\n  });\n  if (doLinesIntersect.features.length > 0) {\n    return true;\n  }\n  return false;\n}\n\n/**\n * Is Polygon (geom1) in Polygon (geom2)\n * Only takes into account outer rings\n * See http://stackoverflow.com/a/4833823/1979085\n *\n * @private\n * @param {Geometry|Feature<Polygon>} feature1 Polygon1\n * @param {Geometry|Feature<Polygon>} feature2 Polygon2\n * @param {boolean} ignoreSelfIntersections ignore self-intersections on input features\n * @returns {boolean} true if geom1 is in geom2, false otherwise\n */\nfunction isPolyInPoly(\n  feature1: Polygon,\n  feature2: Polygon,\n  ignoreSelfIntersections: boolean\n) {\n  for (const coord1 of feature1.coordinates[0]) {\n    if (booleanPointInPolygon(coord1, feature2)) {\n      return true;\n    }\n  }\n  for (const coord2 of feature2.coordinates[0]) {\n    if (booleanPointInPolygon(coord2, feature1)) {\n      return true;\n    }\n  }\n  const doLinesIntersect = lineIntersect(\n    polygonToLine(feature1),\n    polygonToLine(feature2),\n    { ignoreSelfIntersections }\n  );\n  if (doLinesIntersect.features.length > 0) {\n    return true;\n  }\n  return false;\n}\n\nfunction isPointOnLineSegment(\n  lineSegmentStart: Position,\n  lineSegmentEnd: Position,\n  pt: Position\n) {\n  const dxc = pt[0] - lineSegmentStart[0];\n  const dyc = pt[1] - lineSegmentStart[1];\n  const dxl = lineSegmentEnd[0] - lineSegmentStart[0];\n  const dyl = lineSegmentEnd[1] - lineSegmentStart[1];\n  const cross = dxc * dyl - dyc * dxl;\n  if (cross !== 0) {\n    return false;\n  }\n  if (Math.abs(dxl) >= Math.abs(dyl)) {\n    if (dxl > 0) {\n      return lineSegmentStart[0] <= pt[0] && pt[0] <= lineSegmentEnd[0];\n    } else {\n      return lineSegmentEnd[0] <= pt[0] && pt[0] <= lineSegmentStart[0];\n    }\n  } else if (dyl > 0) {\n    return lineSegmentStart[1] <= pt[1] && pt[1] <= lineSegmentEnd[1];\n  } else {\n    return lineSegmentEnd[1] <= pt[1] && pt[1] <= lineSegmentStart[1];\n  }\n}\n\n/**\n * compareCoords\n *\n * @private\n * @param {Position} pair1 point [x,y]\n * @param {Position} pair2 point [x,y]\n * @returns {boolean} true if coord pairs match, false otherwise\n */\nfunction compareCoords(pair1: Position, pair2: Position) {\n  return pair1[0] === pair2[0] && pair1[1] === pair2[1];\n}\n\nexport { booleanDisjoint };\nexport default booleanDisjoint;\n"], "mappings": ";AAQA,SAAS,6BAA6B;AACtC,SAAS,qBAAqB;AAC9B,SAAS,mBAAmB;AAC5B,SAAS,qBAAqB;AAkB9B,SAAS,gBACP,UACA,UACA;AAAA,EACE,0BAA0B;AAC5B,IAEI,EAAE,yBAAyB,KAAK,GAC3B;AACT,MAAI,OAAO;AACX,cAAY,UAAU,CAAC,aAAa;AAClC,gBAAY,UAAU,CAAC,aAAa;AAClC,UAAI,SAAS,OAAO;AAClB,eAAO;AAAA,MACT;AACA,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,QACT;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;AAWA,SAAS,SAAS,OAAY,OAAY,yBAAkC;AAC1E,UAAQ,MAAM,MAAM;AAAA,IAClB,KAAK;AACH,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK;AACH,iBAAO,CAAC,cAAc,MAAM,aAAa,MAAM,WAAW;AAAA,QAC5D,KAAK;AACH,iBAAO,CAAC,cAAc,OAAO,KAAK;AAAA,QACpC,KAAK;AACH,iBAAO,CAAC,sBAAsB,OAAO,KAAK;AAAA,MAC9C;AAEA;AAAA,IACF,KAAK;AACH,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK;AACH,iBAAO,CAAC,cAAc,OAAO,KAAK;AAAA,QACpC,KAAK;AACH,iBAAO,CAAC,aAAa,OAAO,OAAO,uBAAuB;AAAA,QAC5D,KAAK;AACH,iBAAO,CAAC,aAAa,OAAO,OAAO,uBAAuB;AAAA,MAC9D;AAEA;AAAA,IACF,KAAK;AACH,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK;AACH,iBAAO,CAAC,sBAAsB,OAAO,KAAK;AAAA,QAC5C,KAAK;AACH,iBAAO,CAAC,aAAa,OAAO,OAAO,uBAAuB;AAAA,QAC5D,KAAK;AACH,iBAAO,CAAC,aAAa,OAAO,OAAO,uBAAuB;AAAA,MAC9D;AAAA,EACJ;AACA,SAAO;AACT;AAGA,SAAS,cAAc,YAAwB,IAAW;AACxD,WAAS,IAAI,GAAG,IAAI,WAAW,YAAY,SAAS,GAAG,KAAK;AAC1D,QACE;AAAA,MACE,WAAW,YAAY,CAAC;AAAA,MACxB,WAAW,YAAY,IAAI,CAAC;AAAA,MAC5B,GAAG;AAAA,IACL,GACA;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,aACP,aACA,aACA,yBACA;AACA,QAAM,mBAAmB,cAAc,aAAa,aAAa;AAAA,IAC/D;AAAA,EACF,CAAC;AACD,MAAI,iBAAiB,SAAS,SAAS,GAAG;AACxC,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,aACP,SACA,YACA,yBACA;AACA,aAAW,SAAS,WAAW,aAAa;AAC1C,QAAI,sBAAsB,OAAO,OAAO,GAAG;AACzC,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,mBAAmB,cAAc,YAAY,cAAc,OAAO,GAAG;AAAA,IACzE;AAAA,EACF,CAAC;AACD,MAAI,iBAAiB,SAAS,SAAS,GAAG;AACxC,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAaA,SAAS,aACP,UACA,UACA,yBACA;AACA,aAAW,UAAU,SAAS,YAAY,CAAC,GAAG;AAC5C,QAAI,sBAAsB,QAAQ,QAAQ,GAAG;AAC3C,aAAO;AAAA,IACT;AAAA,EACF;AACA,aAAW,UAAU,SAAS,YAAY,CAAC,GAAG;AAC5C,QAAI,sBAAsB,QAAQ,QAAQ,GAAG;AAC3C,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,mBAAmB;AAAA,IACvB,cAAc,QAAQ;AAAA,IACtB,cAAc,QAAQ;AAAA,IACtB,EAAE,wBAAwB;AAAA,EAC5B;AACA,MAAI,iBAAiB,SAAS,SAAS,GAAG;AACxC,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,qBACP,kBACA,gBACA,IACA;AACA,QAAM,MAAM,GAAG,CAAC,IAAI,iBAAiB,CAAC;AACtC,QAAM,MAAM,GAAG,CAAC,IAAI,iBAAiB,CAAC;AACtC,QAAM,MAAM,eAAe,CAAC,IAAI,iBAAiB,CAAC;AAClD,QAAM,MAAM,eAAe,CAAC,IAAI,iBAAiB,CAAC;AAClD,QAAM,QAAQ,MAAM,MAAM,MAAM;AAChC,MAAI,UAAU,GAAG;AACf,WAAO;AAAA,EACT;AACA,MAAI,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,GAAG;AAClC,QAAI,MAAM,GAAG;AACX,aAAO,iBAAiB,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,eAAe,CAAC;AAAA,IAClE,OAAO;AACL,aAAO,eAAe,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,iBAAiB,CAAC;AAAA,IAClE;AAAA,EACF,WAAW,MAAM,GAAG;AAClB,WAAO,iBAAiB,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,eAAe,CAAC;AAAA,EAClE,OAAO;AACL,WAAO,eAAe,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,iBAAiB,CAAC;AAAA,EAClE;AACF;AAUA,SAAS,cAAc,OAAiB,OAAiB;AACvD,SAAO,MAAM,CAAC,MAAM,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,MAAM,CAAC;AACtD;AAGA,IAAO,gCAAQ;", "names": []}