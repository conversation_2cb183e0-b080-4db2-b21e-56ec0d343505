# @turf/boolean-concave

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## booleanConcave

Takes a polygon and return true or false as to whether it is concave or not.

### Parameters

*   `polygon` **[Feature][1]<[Polygon][2]>** to be evaluated

### Examples

```javascript
var convexPolygon = turf.polygon([[[0,0],[0,1],[1,1],[1,0],[0,0]]]);

turf.booleanConcave(convexPolygon)
//=false
```

Returns **[boolean][3]** true/false

[1]: https://tools.ietf.org/html/rfc7946#section-3.2

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/boolean-concave
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```
