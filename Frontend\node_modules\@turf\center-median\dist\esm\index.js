// index.ts
import { centerMean } from "@turf/center-mean";
import { distance } from "@turf/distance";
import { centroid } from "@turf/centroid";
import { isNumber, point, isObject, featureCollection } from "@turf/helpers";
import { featureEach } from "@turf/meta";
function centerMedian(features, options = {}) {
  options = options || {};
  if (!isObject(options)) throw new Error("options is invalid");
  var counter = options.counter || 10;
  if (!isNumber(counter)) throw new Error("counter must be a number");
  var weightTerm = options.weight;
  var meanCenter = centerMean(features, { weight: options.weight });
  var centroids = featureCollection([]);
  featureEach(features, function(feature) {
    var _a;
    centroids.features.push(
      centroid(feature, {
        properties: { weight: (_a = feature.properties) == null ? void 0 : _a[weightTerm] }
      })
    );
  });
  const properties = {
    tolerance: options.tolerance,
    medianCandidates: []
  };
  return findMedian(
    meanCenter.geometry.coordinates,
    [0, 0],
    centroids,
    properties,
    counter
  );
}
function findMedian(candidateMedian, previousCandidate, centroids, properties, counter) {
  var tolerance = properties.tolerance || 1e-3;
  var candidateXsum = 0;
  var candidateYsum = 0;
  var kSum = 0;
  var centroidCount = 0;
  featureEach(centroids, function(theCentroid) {
    var _a;
    var weightValue = (_a = theCentroid.properties) == null ? void 0 : _a.weight;
    var weight = weightValue === void 0 || weightValue === null ? 1 : weightValue;
    weight = Number(weight);
    if (!isNumber(weight)) throw new Error("weight value must be a number");
    if (weight > 0) {
      centroidCount += 1;
      var distanceFromCandidate = weight * distance(theCentroid, candidateMedian);
      if (distanceFromCandidate === 0) distanceFromCandidate = 1;
      var k = weight / distanceFromCandidate;
      candidateXsum += theCentroid.geometry.coordinates[0] * k;
      candidateYsum += theCentroid.geometry.coordinates[1] * k;
      kSum += k;
    }
  });
  if (centroidCount < 1) throw new Error("no features to measure");
  var candidateX = candidateXsum / kSum;
  var candidateY = candidateYsum / kSum;
  if (centroidCount === 1 || counter === 0 || Math.abs(candidateX - previousCandidate[0]) < tolerance && Math.abs(candidateY - previousCandidate[1]) < tolerance) {
    return point([candidateX, candidateY], {
      medianCandidates: properties.medianCandidates
    });
  } else {
    properties.medianCandidates.push([candidateX, candidateY]);
    return findMedian(
      [candidateX, candidateY],
      candidateMedian,
      centroids,
      properties,
      counter - 1
    );
  }
}
var turf_center_median_default = centerMedian;
export {
  centerMedian,
  turf_center_median_default as default
};
//# sourceMappingURL=index.js.map