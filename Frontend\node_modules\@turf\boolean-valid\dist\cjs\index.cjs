"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _invariant = require('@turf/invariant');
var _helpers = require('@turf/helpers');
var _booleandisjoint = require('@turf/boolean-disjoint');
var _booleancrosses = require('@turf/boolean-crosses');
var _lineintersect = require('@turf/line-intersect');
var _booleanpointonline = require('@turf/boolean-point-on-line');
function booleanValid(feature) {
  if (!feature.type) return false;
  const geom = _invariant.getGeom.call(void 0, feature);
  const type = geom.type;
  const coords = geom.coordinates;
  switch (type) {
    case "Point":
      return coords.length > 1;
    case "MultiPoint":
      for (var i = 0; i < coords.length; i++) {
        if (coords[i].length < 2) return false;
      }
      return true;
    case "LineString":
      if (coords.length < 2) return false;
      for (var i = 0; i < coords.length; i++) {
        if (coords[i].length < 2) return false;
      }
      return true;
    case "MultiLineString":
      if (coords.length < 2) return false;
      for (var i = 0; i < coords.length; i++) {
        if (coords[i].length < 2) return false;
      }
      return true;
    case "Polygon":
      for (var i = 0; i < geom.coordinates.length; i++) {
        if (coords[i].length < 4) return false;
        if (!checkRingsClose(coords[i])) return false;
        if (checkRingsForSpikesPunctures(coords[i])) return false;
        if (i > 0) {
          if (_lineintersect.lineIntersect.call(void 0, _helpers.polygon.call(void 0, [coords[0]]), _helpers.polygon.call(void 0, [coords[i]])).features.length > 1)
            return false;
        }
      }
      return true;
    case "MultiPolygon":
      for (var i = 0; i < geom.coordinates.length; i++) {
        var poly = geom.coordinates[i];
        for (var ii = 0; ii < poly.length; ii++) {
          if (poly[ii].length < 4) return false;
          if (!checkRingsClose(poly[ii])) return false;
          if (checkRingsForSpikesPunctures(poly[ii])) return false;
          if (ii === 0) {
            if (!checkPolygonAgainstOthers(poly, geom.coordinates, i))
              return false;
          }
          if (ii > 0) {
            if (_lineintersect.lineIntersect.call(void 0, _helpers.polygon.call(void 0, [poly[0]]), _helpers.polygon.call(void 0, [poly[ii]])).features.length > 1)
              return false;
          }
        }
      }
      return true;
    default:
      return false;
  }
}
function checkRingsClose(geom) {
  return geom[0][0] === geom[geom.length - 1][0] && geom[0][1] === geom[geom.length - 1][1];
}
function checkRingsForSpikesPunctures(geom) {
  for (var i = 0; i < geom.length - 1; i++) {
    var point = geom[i];
    for (var ii = i + 1; ii < geom.length - 2; ii++) {
      var seg = [geom[ii], geom[ii + 1]];
      if (_booleanpointonline.booleanPointOnLine.call(void 0, point, _helpers.lineString.call(void 0, seg))) return true;
    }
  }
  return false;
}
function checkPolygonAgainstOthers(poly, geom, index) {
  var polyToCheck = _helpers.polygon.call(void 0, poly);
  for (var i = index + 1; i < geom.length; i++) {
    if (!_booleandisjoint.booleanDisjoint.call(void 0, polyToCheck, _helpers.polygon.call(void 0, geom[i]))) {
      if (_booleancrosses.booleanCrosses.call(void 0, polyToCheck, _helpers.lineString.call(void 0, geom[i][0]))) return false;
    }
  }
  return true;
}
var turf_boolean_valid_default = booleanValid;



exports.booleanValid = booleanValid; exports.default = turf_boolean_valid_default;
//# sourceMappingURL=index.cjs.map