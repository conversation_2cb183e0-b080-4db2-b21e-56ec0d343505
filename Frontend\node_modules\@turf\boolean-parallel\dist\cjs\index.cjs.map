{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-boolean-parallel/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACCA,iDAA4B;AAC5B,iDAA4B;AAC5B,mDAA6B;AAC7B,wCAAiC;AAgBjC,SAAS,eAAA,CACP,KAAA,EACA,KAAA,EACS;AAET,EAAA,GAAA,CAAI,CAAC,KAAA,EAAO,MAAM,IAAI,KAAA,CAAM,mBAAmB,CAAA;AAC/C,EAAA,GAAA,CAAI,CAAC,KAAA,EAAO,MAAM,IAAI,KAAA,CAAM,mBAAmB,CAAA;AAC/C,EAAA,IAAI,MAAA,EAAQ,OAAA,CAAQ,KAAA,EAAO,OAAO,CAAA;AAClC,EAAA,GAAA,CAAI,MAAA,IAAU,YAAA,EAAc,MAAM,IAAI,KAAA,CAAM,4BAA4B,CAAA;AACxE,EAAA,IAAI,MAAA,EAAQ,OAAA,CAAQ,KAAA,EAAO,OAAO,CAAA;AAClC,EAAA,GAAA,CAAI,MAAA,IAAU,YAAA,EAAc,MAAM,IAAI,KAAA,CAAM,4BAA4B,CAAA;AAExE,EAAA,IAAI,UAAA,EAAY,sCAAA,sCAAY,KAAiB,CAAC,CAAA,CAAE,QAAA;AAChD,EAAA,IAAI,UAAA,EAAY,sCAAA,sCAAY,KAAiB,CAAC,CAAA,CAAE,QAAA;AAEhD,EAAA,IAAA,CAAA,IAAS,EAAA,EAAI,CAAA,EAAG,EAAA,EAAI,SAAA,CAAU,MAAA,EAAQ,CAAA,EAAA,EAAK;AACzC,IAAA,IAAI,SAAA,EAAW,SAAA,CAAU,CAAC,CAAA,CAAE,QAAA,CAAS,WAAA;AACrC,IAAA,GAAA,CAAI,CAAC,SAAA,CAAU,CAAC,CAAA,EAAG,KAAA;AACnB,IAAA,IAAI,SAAA,EAAW,SAAA,CAAU,CAAC,CAAA,CAAE,QAAA,CAAS,WAAA;AACrC,IAAA,GAAA,CAAI,CAAC,UAAA,CAAW,QAAA,EAAU,QAAQ,CAAA,EAAG,OAAO,KAAA;AAAA,EAC9C;AACA,EAAA,OAAO,IAAA;AACT;AAUA,SAAS,UAAA,CAAW,QAAA,EAAsB,QAAA,EAAsB;AAC9D,EAAA,IAAI,OAAA,EAAS,uCAAA,wCAAiB,QAAa,CAAS,CAAC,CAAA,EAAG,QAAA,CAAS,CAAC,CAAC,CAAC,CAAA;AACpE,EAAA,IAAI,OAAA,EAAS,uCAAA,wCAAiB,QAAa,CAAS,CAAC,CAAA,EAAG,QAAA,CAAS,CAAC,CAAC,CAAC,CAAA;AACpE,EAAA,OAAO,OAAA,IAAW,OAAA,GAAA,CAAW,OAAA,EAAS,MAAA,EAAA,EAAU,IAAA,IAAQ,CAAA;AAC1D;AAUA,SAAS,OAAA,CAAQ,OAAA,EAAkC,IAAA,EAAc;AAC/D,EAAA,GAAA,CAAK,OAAA,CAAoB,SAAA,GAAa,OAAA,CAAoB,QAAA,CAAS,IAAA;AACjE,IAAA,OAAQ,OAAA,CAAoB,QAAA,CAAS,IAAA;AACvC,EAAA,GAAA,CAAI,OAAA,CAAQ,IAAA,EAAM,OAAO,OAAA,CAAQ,IAAA;AACjC,EAAA,MAAM,IAAI,KAAA,CAAM,8BAAA,EAAgC,IAAI,CAAA;AACtD;AAGA,IAAO,8BAAA,EAAQ,eAAA;ADxCf;AACE;AACA;AACF,2FAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-boolean-parallel/dist/cjs/index.cjs", "sourcesContent": [null, "import { Feature, Geometry, LineString, Position } from \"geojson\";\nimport { cleanCoords } from \"@turf/clean-coords\";\nimport { lineSegment } from \"@turf/line-segment\";\nimport { rhumbBearing } from \"@turf/rhumb-bearing\";\nimport { bearingToAzimuth } from \"@turf/helpers\";\n\n/**\n * <PERSON><PERSON><PERSON>-<PERSON><PERSON>l returns True if each segment of `line1` is parallel to the correspondent segment of `line2`\n *\n * @function\n * @param {Geometry|Feature<LineString>} line1 GeoJSON Feature or Geometry\n * @param {Geometry|Feature<LineString>} line2 GeoJSON Feature or Geometry\n * @returns {boolean} true/false if the lines are parallel\n * @example\n * var line1 = turf.lineString([[0, 0], [0, 1]]);\n * var line2 = turf.lineString([[1, 0], [1, 1]]);\n *\n * turf.booleanParallel(line1, line2);\n * //=true\n */\nfunction booleanParallel(\n  line1: Feature<LineString> | LineString,\n  line2: Feature<LineString> | LineString\n): boolean {\n  // validation\n  if (!line1) throw new Error(\"line1 is required\");\n  if (!line2) throw new Error(\"line2 is required\");\n  var type1 = getType(line1, \"line1\");\n  if (type1 !== \"LineString\") throw new Error(\"line1 must be a LineString\");\n  var type2 = getType(line2, \"line2\");\n  if (type2 !== \"LineString\") throw new Error(\"line2 must be a LineString\");\n\n  var segments1 = lineSegment(cleanCoords(line1)).features;\n  var segments2 = lineSegment(cleanCoords(line2)).features;\n\n  for (var i = 0; i < segments1.length; i++) {\n    var segment1 = segments1[i].geometry.coordinates;\n    if (!segments2[i]) break;\n    var segment2 = segments2[i].geometry.coordinates;\n    if (!isParallel(segment1, segment2)) return false;\n  }\n  return true;\n}\n\n/**\n * Compares slopes and return result\n *\n * @private\n * @param {Geometry|Feature<LineString>} segment1 Geometry or Feature\n * @param {Geometry|Feature<LineString>} segment2 Geometry or Feature\n * @returns {boolean} if slopes are equal\n */\nfunction isParallel(segment1: Position[], segment2: Position[]) {\n  var slope1 = bearingToAzimuth(rhumbBearing(segment1[0], segment1[1]));\n  var slope2 = bearingToAzimuth(rhumbBearing(segment2[0], segment2[1]));\n  return slope1 === slope2 || (slope2 - slope1) % 180 === 0;\n}\n\n/**\n * Returns Feature's type\n *\n * @private\n * @param {Geometry|Feature<any>} geojson Geometry or Feature\n * @param {string} name of the variable\n * @returns {string} Feature's type\n */\nfunction getType(geojson: Geometry | Feature<any>, name: string) {\n  if ((geojson as Feature).geometry && (geojson as Feature).geometry.type)\n    return (geojson as Feature).geometry.type;\n  if (geojson.type) return geojson.type; // if GeoJSON geometry\n  throw new Error(\"Invalid GeoJSON object for \" + name);\n}\n\nexport { booleanParallel };\nexport default booleanParallel;\n"]}