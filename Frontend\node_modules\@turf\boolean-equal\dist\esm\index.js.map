{"version": 3, "sources": ["../../index.ts"], "sourcesContent": ["import { Feature, Geometry } from \"geojson\";\nimport { geojsonEquality } from \"geojson-equality-ts\";\nimport { cleanCoords } from \"@turf/clean-coords\";\nimport { getGeom } from \"@turf/invariant\";\n\n/**\n * Determine whether two geometries of the same type have identical X,Y coordinate values.\n * See http://edndoc.esri.com/arcsde/9.0/general_topics/understand_spatial_relations.htm\n *\n * @function\n * @param {Geometry|Feature} feature1 GeoJSON input\n * @param {Geometry|Feature} feature2 GeoJSON input\n * @param {Object} [options={}] Optional parameters\n * @param {number} [options.precision=6] decimal precision to use when comparing coordinates\n * @returns {boolean} true if the objects are equal, false otherwise\n * @example\n * var pt1 = turf.point([0, 0]);\n * var pt2 = turf.point([0, 0]);\n * var pt3 = turf.point([1, 1]);\n *\n * turf.booleanEqual(pt1, pt2);\n * //= true\n * turf.booleanEqual(pt2, pt3);\n * //= false\n */\nfunction booleanEqual(\n  feature1: Feature<any> | Geometry,\n  feature2: Feature<any> | Geometry,\n  options: {\n    precision?: number;\n  } = {}\n): boolean {\n  let precision = options.precision;\n\n  precision =\n    precision === undefined || precision === null || isNaN(precision)\n      ? 6\n      : precision;\n\n  if (typeof precision !== \"number\" || !(precision >= 0)) {\n    throw new Error(\"precision must be a positive number\");\n  }\n\n  const type1 = getGeom(feature1).type;\n  const type2 = getGeom(feature2).type;\n  if (type1 !== type2) return false;\n\n  return geojsonEquality(cleanCoords(feature1), cleanCoords(feature2), {\n    precision,\n  });\n}\n\nexport { booleanEqual };\nexport default booleanEqual;\n"], "mappings": ";AACA,SAAS,uBAAuB;AAChC,SAAS,mBAAmB;AAC5B,SAAS,eAAe;AAsBxB,SAAS,aACP,UACA,UACA,UAEI,CAAC,GACI;AACT,MAAI,YAAY,QAAQ;AAExB,cACE,cAAc,UAAa,cAAc,QAAQ,MAAM,SAAS,IAC5D,IACA;AAEN,MAAI,OAAO,cAAc,YAAY,EAAE,aAAa,IAAI;AACtD,UAAM,IAAI,MAAM,qCAAqC;AAAA,EACvD;AAEA,QAAM,QAAQ,QAAQ,QAAQ,EAAE;AAChC,QAAM,QAAQ,QAAQ,QAAQ,EAAE;AAChC,MAAI,UAAU,MAAO,QAAO;AAE5B,SAAO,gBAAgB,YAAY,QAAQ,GAAG,YAAY,QAAQ,GAAG;AAAA,IACnE;AAAA,EACF,CAAC;AACH;AAGA,IAAO,6BAAQ;", "names": []}