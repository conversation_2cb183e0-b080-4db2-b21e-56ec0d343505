// index.ts
import { bearing } from "@turf/bearing";
import { destination } from "@turf/destination";
import { distance as measureDistance } from "@turf/distance";
import { point } from "@turf/helpers";
import { getGeom } from "@turf/invariant";
function along(line, distance, options = {}) {
  const geom = getGeom(line);
  const coords = geom.coordinates;
  let travelled = 0;
  for (let i = 0; i < coords.length; i++) {
    if (distance >= travelled && i === coords.length - 1) {
      break;
    } else if (travelled >= distance) {
      const overshot = distance - travelled;
      if (!overshot) {
        return point(coords[i]);
      } else {
        const direction = bearing(coords[i], coords[i - 1]) - 180;
        const interpolated = destination(
          coords[i],
          overshot,
          direction,
          options
        );
        return interpolated;
      }
    } else {
      travelled += measureDistance(coords[i], coords[i + 1], options);
    }
  }
  return point(coords[coords.length - 1]);
}
var turf_along_default = along;
export {
  along,
  turf_along_default as default
};
//# sourceMappingURL=index.js.map