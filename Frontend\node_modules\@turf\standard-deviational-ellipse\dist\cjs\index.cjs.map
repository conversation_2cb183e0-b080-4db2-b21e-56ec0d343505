{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-standard-deviational-ellipse/dist/cjs/index.cjs", "../../index.ts"], "names": ["_a"], "mappings": "AAAA;ACQA,kCAAsC;AACtC,4CAA0B;AAC1B,wCAAsD;AACtD,+CAA2B;AAC3B,kEAAoC;AACpC,wCAAwB;AAwDxB,SAAS,0BAAA,CACP,MAAA,EACA,OAAA,EAK4B;AA5E9B,EAAA,IAAA,EAAA;AA8EE,EAAA,QAAA,EAAU,QAAA,GAAW,CAAC,CAAA;AACtB,EAAA,GAAA,CAAI,CAAC,+BAAA,OAAgB,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AAC5D,EAAA,MAAM,MAAA,EAAQ,OAAA,CAAQ,MAAA,GAAS,EAAA;AAC/B,EAAA,MAAM,WAAA,EAAa,OAAA,CAAQ,MAAA;AAC3B,EAAA,MAAM,WAAA,EAAa,OAAA,CAAQ,WAAA,GAAc,CAAC,CAAA;AAG1C,EAAA,GAAA,CAAI,CAAC,+BAAA,KAAc,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,wBAAwB,CAAA;AAC9D,EAAA,GAAA,CAAI,CAAC,+BAAA,UAAmB,CAAA,EAAG,MAAM,IAAI,KAAA,CAAM,6BAA6B,CAAA;AAGxE,EAAA,MAAM,iBAAA,EAAmB,4BAAA,MAAe,CAAA,CAAE,MAAA;AAC1C,EAAA,MAAM,WAAA,EAAa,oCAAA,MAAW,EAAQ,EAAE,MAAA,EAAQ,WAAW,CAAC,CAAA;AAS5D,EAAA,IAAI,qBAAA,EAAuB,CAAA;AAC3B,EAAA,IAAI,qBAAA,EAAuB,CAAA;AAC3B,EAAA,IAAI,eAAA,EAAiB,CAAA;AAErB,EAAA,+BAAA,MAAY,EAAQ,QAAA,CAAU,KAAA,EAAO;AAvGvC,IAAA,IAAAA,GAAAA;AAyGI,IAAA,MAAM,OAAA,EAAS,WAAA,EAAA,CAAA,CAAaA,IAAAA,EAAA,KAAA,CAAM,UAAA,EAAA,GAAN,KAAA,EAAA,KAAA,EAAA,EAAAA,GAAAA,CAAmB,UAAA,CAAA,EAAA,GAAe,EAAA,EAAI,CAAA;AAClE,IAAA,MAAM,UAAA,EAAY,aAAA,CAAc,kCAAA,KAAe,CAAA,EAAG,kCAAA,UAAoB,CAAC,CAAA;AACvE,IAAA,qBAAA,GAAwB,IAAA,CAAK,GAAA,CAAI,SAAA,CAAU,CAAA,EAAG,CAAC,EAAA,EAAI,MAAA;AACnD,IAAA,qBAAA,GAAwB,IAAA,CAAK,GAAA,CAAI,SAAA,CAAU,CAAA,EAAG,CAAC,EAAA,EAAI,MAAA;AACnD,IAAA,eAAA,GAAkB,SAAA,CAAU,EAAA,EAAI,SAAA,CAAU,EAAA,EAAI,MAAA;AAAA,EAChD,CAAC,CAAA;AAED,EAAA,MAAM,KAAA,EAAO,qBAAA,EAAuB,oBAAA;AACpC,EAAA,MAAM,KAAA,EAAO,IAAA,CAAK,IAAA,CAAK,IAAA,CAAK,GAAA,CAAI,IAAA,EAAM,CAAC,EAAA,EAAI,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,cAAA,EAAgB,CAAC,CAAC,CAAA;AAC1E,EAAA,MAAM,KAAA,EAAO,EAAA,EAAI,cAAA;AACjB,EAAA,MAAM,MAAA,EAAQ,IAAA,CAAK,IAAA,CAAA,CAAM,KAAA,EAAO,IAAA,EAAA,EAAQ,IAAI,CAAA;AAC5C,EAAA,MAAM,SAAA,EAAY,MAAA,EAAQ,IAAA,EAAO,IAAA,CAAK,EAAA;AAKtC,EAAA,IAAI,UAAA,EAAY,CAAA;AAChB,EAAA,IAAI,UAAA,EAAY,CAAA;AAChB,EAAA,IAAI,UAAA,EAAY,CAAA;AAChB,EAAA,+BAAA,MAAY,EAAQ,QAAA,CAAU,KAAA,EAAO;AA5HvC,IAAA,IAAAA,GAAAA;AA8HI,IAAA,MAAM,OAAA,EAAS,WAAA,EAAA,CAAA,CAAaA,IAAAA,EAAA,KAAA,CAAM,UAAA,EAAA,GAAN,KAAA,EAAA,KAAA,EAAA,EAAAA,GAAAA,CAAmB,UAAA,CAAA,EAAA,GAAe,EAAA,EAAI,CAAA;AAClE,IAAA,MAAM,UAAA,EAAY,aAAA,CAAc,kCAAA,KAAe,CAAA,EAAG,kCAAA,UAAoB,CAAC,CAAA;AACvE,IAAA,UAAA,GACE,IAAA,CAAK,GAAA;AAAA,MACH,SAAA,CAAU,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,KAAK,EAAA,EAAI,SAAA,CAAU,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,KAAK,CAAA;AAAA,MAC5D;AAAA,IACF,EAAA,EAAI,MAAA;AACN,IAAA,UAAA,GACE,IAAA,CAAK,GAAA;AAAA,MACH,SAAA,CAAU,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,KAAK,EAAA,EAAI,SAAA,CAAU,EAAA,EAAI,IAAA,CAAK,GAAA,CAAI,KAAK,CAAA;AAAA,MAC5D;AAAA,IACF,EAAA,EAAI,MAAA;AACN,IAAA,UAAA,GAAa,MAAA;AAAA,EACf,CAAC,CAAA;AAED,EAAA,MAAM,OAAA,EAAS,IAAA,CAAK,IAAA,CAAM,EAAA,EAAI,UAAA,EAAa,SAAS,CAAA;AACpD,EAAA,MAAM,OAAA,EAAS,IAAA,CAAK,IAAA,CAAM,EAAA,EAAI,UAAA,EAAa,SAAS,CAAA;AAEpD,EAAA,MAAM,WAAA,EAA+B,8BAAA,UAAQ,EAAY,MAAA,EAAQ,MAAA,EAAQ;AAAA,IACvE,KAAA,EAAO,SAAA;AAAA,IACP,KAAA,EAAO,QAAA;AAAA,IACP,KAAA;AAAA,IACA;AAAA,EACF,CAAC,CAAA;AACD,EAAA,MAAM,oBAAA,EAAsB,sDAAA;AAAA,IAC1B,MAAA;AAAA,IACA,wCAAA,CAAmB,UAAU,CAAC;AAAA,EAChC,CAAA;AACA,EAAA,MAAM,qCAAA,EAAuC;AAAA,IAC3C,qBAAA,EAAuB,kCAAA,UAAoB,CAAA;AAAA,IAC3C,aAAA,EAAe,MAAA;AAAA,IACf,aAAA,EAAe,MAAA;AAAA,IACf,gBAAA;AAAA,IACA,KAAA,EAAO,QAAA;AAAA,IACP,uBAAA,EACG,IAAA,EAAM,4BAAA,mBAA4B,CAAA,CAAE,OAAA,EAAU;AAAA,EACnD,CAAA;AAEA,EAAA,UAAA,CAAW,WAAA,EAAA,CAAa,GAAA,EAAA,UAAA,CAAW,UAAA,EAAA,GAAX,KAAA,EAAA,GAAA,EAAyB,CAAC,CAAA;AAClD,EAAA,UAAA,CAAW,UAAA,CAAW,2BAAA,EACpB,oCAAA;AAIF,EAAA,OAAO,UAAA;AACT;AAUA,SAAS,aAAA,CAAc,WAAA,EAAuB,MAAA,EAAkB;AAC9D,EAAA,OAAO;AAAA,IACL,CAAA,EAAG,WAAA,CAAY,CAAC,EAAA,EAAI,MAAA,CAAO,CAAC,CAAA;AAAA,IAC5B,CAAA,EAAG,WAAA,CAAY,CAAC,EAAA,EAAI,MAAA,CAAO,CAAC;AAAA,EAC9B,CAAA;AACF;AAGA,IAAO,0CAAA,EAAQ,0BAAA;AD3Gf;AACE;AACA;AACF,6HAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-standard-deviational-ellipse/dist/cjs/index.cjs", "sourcesContent": [null, "import {\n  FeatureCollection,\n  Feature,\n  Position,\n  Polygon,\n  GeoJsonProperties,\n  Point,\n} from \"geojson\";\nimport { coordAll, featureEach } from \"@turf/meta\";\nimport { getCoords } from \"@turf/invariant\";\nimport { featureCollection, isObject, isNumber } from \"@turf/helpers\";\nimport { centerMean } from \"@turf/center-mean\";\nimport { pointsWithinPolygon } from \"@turf/points-within-polygon\";\nimport { ellipse } from \"@turf/ellipse\";\n\ndeclare interface SDEProps {\n  meanCenterCoordinates: Position;\n  semiMajorAxis: number;\n  semiMinorAxis: number;\n  numberOfFeatures: number;\n  angle: number;\n  percentageWithinEllipse: number;\n}\n\ndeclare interface StandardDeviationalEllipse extends Feature<Polygon> {\n  properties: {\n    standardDeviationalEllipse: SDEProps;\n    [key: string]: any;\n  } | null;\n}\n\n/**\n * Takes a collection of features and returns a standard deviational ellipse,\n * also known as a “directional distribution.” The standard deviational ellipse\n * aims to show the direction and the distribution of a dataset by drawing\n * an ellipse that contains about one standard deviation’s worth (~ 70%) of the\n * data.\n *\n * This module mirrors the functionality of {@link http://desktop.arcgis.com/en/arcmap/10.3/tools/spatial-statistics-toolbox/directional-distribution.htm|Directional Distribution}\n * in ArcGIS and the {@link http://arken.nmbu.no/~havatv/gis/qgisplugins/SDEllipse/|QGIS Standard Deviational Ellipse Plugin}\n *\n * **Bibliography**\n *\n * • Robert S. Yuill, “The Standard Deviational Ellipse; An Updated Tool for\n * Spatial Description,” _Geografiska Annaler_ 53, no. 1 (1971): 28–39,\n * doi:{@link https://doi.org/10.2307/490885|10.2307/490885}.\n *\n * • Paul Hanly Furfey, “A Note on Lefever’s “Standard Deviational Ellipse,”\n * _American Journal of Sociology_ 33, no. 1 (1927): 94—98,\n * doi:{@link https://doi.org/10.1086/214336|10.1086/214336}.\n *\n *\n * @function\n * @param {FeatureCollection<Point>} points GeoJSON points\n * @param {Object} [options={}] Optional parameters\n * @param {string} [options.weight] the property name used to weight the center\n * @param {number} [options.steps=64] number of steps for the polygon\n * @param {Object} [options.properties={}] properties to pass to the resulting ellipse\n * @returns {Feature<Polygon>} an elliptical Polygon that includes approximately 1 SD of the dataset within it.\n * @example\n *\n * const bbox = [-74, 40.72, -73.98, 40.74];\n * const points = turf.randomPoint(400, {bbox: bbox});\n * const sdEllipse = turf.standardDeviationalEllipse(points);\n *\n * //addToMap\n * const addToMap = [points, sdEllipse];\n *\n */\nfunction standardDeviationalEllipse(\n  points: FeatureCollection<Point>,\n  options?: {\n    properties?: GeoJsonProperties;\n    weight?: string;\n    steps?: number;\n  }\n): StandardDeviationalEllipse {\n  // Optional params\n  options = options || {};\n  if (!isObject(options)) throw new Error(\"options is invalid\");\n  const steps = options.steps || 64;\n  const weightTerm = options.weight;\n  const properties = options.properties || {};\n\n  // Validation:\n  if (!isNumber(steps)) throw new Error(\"steps must be a number\");\n  if (!isObject(properties)) throw new Error(\"properties must be a number\");\n\n  // Calculate mean center & number of features:\n  const numberOfFeatures = coordAll(points).length;\n  const meanCenter = centerMean(points, { weight: weightTerm });\n\n  // Calculate angle of rotation:\n  // [X, Y] = mean center of all [x, y].\n  // theta = arctan( (A + B) / C )\n  // A = sum((x - X)^2) - sum((y - Y)^2)\n  // B = sqrt(A^2 + 4(sum((x - X)(y - Y))^2))\n  // C = 2(sum((x - X)(y - Y)))\n\n  let xDeviationSquaredSum = 0;\n  let yDeviationSquaredSum = 0;\n  let xyDeviationSum = 0;\n\n  featureEach(points, function (point) {\n    // weightTerm or point.properties might be undefined, hence this check.\n    const weight = weightTerm ? point.properties?.[weightTerm] || 1 : 1;\n    const deviation = getDeviations(getCoords(point), getCoords(meanCenter));\n    xDeviationSquaredSum += Math.pow(deviation.x, 2) * weight;\n    yDeviationSquaredSum += Math.pow(deviation.y, 2) * weight;\n    xyDeviationSum += deviation.x * deviation.y * weight;\n  });\n\n  const bigA = xDeviationSquaredSum - yDeviationSquaredSum;\n  const bigB = Math.sqrt(Math.pow(bigA, 2) + 4 * Math.pow(xyDeviationSum, 2));\n  const bigC = 2 * xyDeviationSum;\n  const theta = Math.atan((bigA + bigB) / bigC);\n  const thetaDeg = (theta * 180) / Math.PI;\n\n  // Calculate axes:\n  // sigmaX = sqrt((1 / n - 2) * sum((((x - X) * cos(theta)) - ((y - Y) * sin(theta)))^2))\n  // sigmaY = sqrt((1 / n - 2) * sum((((x - X) * sin(theta)) - ((y - Y) * cos(theta)))^2))\n  let sigmaXsum = 0;\n  let sigmaYsum = 0;\n  let weightsum = 0;\n  featureEach(points, function (point) {\n    // weightTerm or point.properties might be undefined, hence this check.\n    const weight = weightTerm ? point.properties?.[weightTerm] || 1 : 1;\n    const deviation = getDeviations(getCoords(point), getCoords(meanCenter));\n    sigmaXsum +=\n      Math.pow(\n        deviation.x * Math.cos(theta) - deviation.y * Math.sin(theta),\n        2\n      ) * weight;\n    sigmaYsum +=\n      Math.pow(\n        deviation.x * Math.sin(theta) + deviation.y * Math.cos(theta),\n        2\n      ) * weight;\n    weightsum += weight;\n  });\n\n  const sigmaX = Math.sqrt((2 * sigmaXsum) / weightsum);\n  const sigmaY = Math.sqrt((2 * sigmaYsum) / weightsum);\n\n  const theEllipse: Feature<Polygon> = ellipse(meanCenter, sigmaX, sigmaY, {\n    units: \"degrees\",\n    angle: thetaDeg,\n    steps: steps,\n    properties: properties,\n  });\n  const pointsWithinEllipse = pointsWithinPolygon(\n    points,\n    featureCollection([theEllipse])\n  );\n  const standardDeviationalEllipseProperties = {\n    meanCenterCoordinates: getCoords(meanCenter),\n    semiMajorAxis: sigmaX,\n    semiMinorAxis: sigmaY,\n    numberOfFeatures: numberOfFeatures,\n    angle: thetaDeg,\n    percentageWithinEllipse:\n      (100 * coordAll(pointsWithinEllipse).length) / numberOfFeatures,\n  };\n  // Make sure properties object exists.\n  theEllipse.properties = theEllipse.properties ?? {};\n  theEllipse.properties.standardDeviationalEllipse =\n    standardDeviationalEllipseProperties;\n\n  // We have added the StandardDeviationalEllipse specific properties, so\n  // confirm this to Typescript with a cast.\n  return theEllipse as StandardDeviationalEllipse;\n}\n\n/**\n * Get x_i - X and y_i - Y\n *\n * @private\n * @param {Position} coordinates Array of [x_i, y_i]\n * @param {Position} center Array of [X, Y]\n * @returns {Object} { x: n, y: m }\n */\nfunction getDeviations(coordinates: Position, center: Position) {\n  return {\n    x: coordinates[0] - center[0],\n    y: coordinates[1] - center[1],\n  };\n}\n\nexport { standardDeviationalEllipse, SDEProps, StandardDeviationalEllipse };\nexport default standardDeviationalEllipse;\n"]}