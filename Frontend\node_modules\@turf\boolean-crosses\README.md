# @turf/boolean-crosses

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## booleanCrosses

Boolean-Crosses returns True if the intersection results in a geometry whose dimension is one less than
the maximum dimension of the two source geometries and the intersection set is interior to
both source geometries.

Boolean-Crosses returns t (TRUE) for only multipoint/polygon, multipoint/linestring, linestring/linestring, linestring/polygon, and linestring/multipolygon comparisons.
Other comparisons are not supported as they are outside the OpenGIS Simple Features spec and may give unexpected results.

### Parameters

*   `feature1` **([Geometry][1] | [Feature][2]\<any>)** GeoJSON Feature or Geometry
*   `feature2` **([Geometry][1] | [Feature][2]\<any>)** GeoJSON Feature or Geometry

### Examples

```javascript
var line1 = turf.lineString([[-2, 2], [4, 2]]);
var line2 = turf.lineString([[1, 1], [1, 2], [1, 3], [1, 4]]);

var cross = turf.booleanCrosses(line1, line2);
//=true
```

Returns **[boolean][3]** true/false

[1]: https://tools.ietf.org/html/rfc7946#section-3.1

[2]: https://tools.ietf.org/html/rfc7946#section-3.2

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

<!-- This file is automatically generated. Please don't edit it directly. If you find an error, edit the source file of the module in question (likely index.js or index.ts), and re-run "yarn docs" from the root of the turf project. -->

---

This module is part of the [Turfjs project](https://turfjs.org/), an open source module collection dedicated to geographic algorithms. It is maintained in the [Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create PRs and issues.

### Installation

Install this single module individually:

```sh
$ npm install @turf/boolean-crosses
```

Or install the all-encompassing @turf/turf module that includes all modules as functions:

```sh
$ npm install @turf/turf
```


### Diagrams

![esri-crosses](diagrams/esri-crosses.gif)