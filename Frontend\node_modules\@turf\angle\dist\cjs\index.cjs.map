{"version": 3, "sources": ["/home/<USER>/work/turf/turf/packages/turf-angle/dist/cjs/index.cjs", "../../index.ts"], "names": [], "mappings": "AAAA;ACAA,wCAAwB;AACxB,wCAAkD;AAClD,mDAA6B;AAkB7B,SAAS,KAAA,CACP,UAAA,EACA,QAAA,EACA,QAAA,EACA,QAAA,EAGI,CAAC,CAAA,EACG;AAER,EAAA,GAAA,CAAI,CAAC,+BAAA,OAAgB,CAAA,EAAG;AACtB,IAAA,MAAM,IAAI,KAAA,CAAM,oBAAoB,CAAA;AAAA,EACtC;AAGA,EAAA,GAAA,CAAI,CAAC,UAAA,EAAY;AACf,IAAA,MAAM,IAAI,KAAA,CAAM,wBAAwB,CAAA;AAAA,EAC1C;AACA,EAAA,GAAA,CAAI,CAAC,QAAA,EAAU;AACb,IAAA,MAAM,IAAI,KAAA,CAAM,sBAAsB,CAAA;AAAA,EACxC;AACA,EAAA,GAAA,CAAI,CAAC,QAAA,EAAU;AACb,IAAA,MAAM,IAAI,KAAA,CAAM,sBAAsB,CAAA;AAAA,EACxC;AAGA,EAAA,MAAM,EAAA,EAAI,UAAA;AACV,EAAA,MAAM,EAAA,EAAI,QAAA;AACV,EAAA,MAAM,EAAA,EAAI,QAAA;AAGV,EAAA,MAAM,UAAA,EAAY,uCAAA;AAAA,IAChB,OAAA,CAAQ,SAAA,IAAa,KAAA,EAAO,8BAAA,CAAQ,EAAG,CAAC,EAAA,EAAI,wCAAA,CAAa,EAAG,CAAC;AAAA,EAC/D,CAAA;AACA,EAAA,IAAI,UAAA,EAAY,uCAAA;AAAA,IACd,OAAA,CAAQ,SAAA,IAAa,KAAA,EAAO,8BAAA,CAAQ,EAAG,CAAC,EAAA,EAAI,wCAAA,CAAa,EAAG,CAAC;AAAA,EAC/D,CAAA;AAEA,EAAA,GAAA,CAAI,UAAA,EAAY,SAAA,EAAW;AACzB,IAAA,UAAA,EAAY,UAAA,EAAY,GAAA;AAAA,EAC1B;AACA,EAAA,MAAM,SAAA,EAAW,UAAA,EAAY,SAAA;AAG7B,EAAA,GAAA,CAAI,OAAA,CAAQ,aAAA,IAAiB,IAAA,EAAM;AACjC,IAAA,OAAO,IAAA,EAAM,QAAA;AAAA,EACf;AACA,EAAA,OAAO,QAAA;AACT;AAGA,IAAO,mBAAA,EAAQ,KAAA;ADnCf;AACE;AACA;AACF,4DAAC", "file": "/home/<USER>/work/turf/turf/packages/turf-angle/dist/cjs/index.cjs", "sourcesContent": [null, "import { bearing } from \"@turf/bearing\";\nimport { bearingToAzimuth, Coord, isObject } from \"@turf/helpers\";\nimport { rhumbBearing } from \"@turf/rhumb-bearing\";\n\n/**\n * Finds the angle formed by two adjacent segments defined by 3 points. The result will be the (positive clockwise)\n * angle with origin on the `startPoint-midPoint` segment, or its explementary angle if required.\n *\n * @function\n * @param {Coord} startPoint Start Point Coordinates\n * @param {Coord} midPoint Mid Point Coordinates\n * @param {Coord} endPoint End Point Coordinates\n * @param {Object} [options={}] Optional parameters\n * @param {boolean} [options.explementary=false] Returns the explementary angle instead (360 - angle)\n * @param {boolean} [options.mercator=false] if calculations should be performed over Mercator or WGS84 projection\n * @returns {number} Angle between the provided points, or its explementary.\n * @example\n * turf.angle([5, 5], [5, 6], [3, 4]);\n * //=45\n */\nfunction angle(\n  startPoint: Coord,\n  midPoint: Coord,\n  endPoint: Coord,\n  options: {\n    explementary?: boolean;\n    mercator?: boolean;\n  } = {}\n): number {\n  // Optional Parameters\n  if (!isObject(options)) {\n    throw new Error(\"options is invalid\");\n  }\n\n  // Validation\n  if (!startPoint) {\n    throw new Error(\"startPoint is required\");\n  }\n  if (!midPoint) {\n    throw new Error(\"midPoint is required\");\n  }\n  if (!endPoint) {\n    throw new Error(\"endPoint is required\");\n  }\n\n  // Rename to shorter variables\n  const A = startPoint;\n  const O = midPoint;\n  const B = endPoint;\n\n  // Main\n  const azimuthOA = bearingToAzimuth(\n    options.mercator !== true ? bearing(O, A) : rhumbBearing(O, A)\n  );\n  let azimuthOB = bearingToAzimuth(\n    options.mercator !== true ? bearing(O, B) : rhumbBearing(O, B)\n  );\n  // If OB \"trails\" OA advance OB one revolution so we get the clockwise angle.\n  if (azimuthOB < azimuthOA) {\n    azimuthOB = azimuthOB + 360;\n  }\n  const angleAOB = azimuthOB - azimuthOA;\n\n  // Explementary angle\n  if (options.explementary === true) {\n    return 360 - angleAOB;\n  }\n  return angleAOB;\n}\n\nexport { angle };\nexport default angle;\n"]}