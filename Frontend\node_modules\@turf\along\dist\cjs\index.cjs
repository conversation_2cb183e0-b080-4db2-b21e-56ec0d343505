"use strict";Object.defineProperty(exports, "__esModule", {value: true});// index.ts
var _bearing = require('@turf/bearing');
var _destination = require('@turf/destination');
var _distance = require('@turf/distance');
var _helpers = require('@turf/helpers');
var _invariant = require('@turf/invariant');
function along(line, distance, options = {}) {
  const geom = _invariant.getGeom.call(void 0, line);
  const coords = geom.coordinates;
  let travelled = 0;
  for (let i = 0; i < coords.length; i++) {
    if (distance >= travelled && i === coords.length - 1) {
      break;
    } else if (travelled >= distance) {
      const overshot = distance - travelled;
      if (!overshot) {
        return _helpers.point.call(void 0, coords[i]);
      } else {
        const direction = _bearing.bearing.call(void 0, coords[i], coords[i - 1]) - 180;
        const interpolated = _destination.destination.call(void 0, 
          coords[i],
          overshot,
          direction,
          options
        );
        return interpolated;
      }
    } else {
      travelled += _distance.distance.call(void 0, coords[i], coords[i + 1], options);
    }
  }
  return _helpers.point.call(void 0, coords[coords.length - 1]);
}
var turf_along_default = along;



exports.along = along; exports.default = turf_along_default;
//# sourceMappingURL=index.cjs.map